<?php
/**
 * 测试major字段同步功能
 * 验证当majorTxt被更新时，major数组字段是否会被正确同步
 */

require_once __DIR__ . '/common/base/models/BaseSpecialNeedConfig.php';

echo "=== 测试major字段同步功能 ===\n\n";

// 模拟H5端的数据结构（您提到的情况）
$h5Data = [
    'id' => 123,
    'title' => '软件工程师',
    'majorTxt' => '临床医学',  // 原始文本字段
    'major' => [              // 原始数组字段
        [
            'majorText' => '旧的专业文本',
            'url' => '#'
        ]
    ],
    'education' => '本科'
];

echo "原始H5数据:\n";
print_r($h5Data);

// 模拟配置数据
$configs = [
    [
        'field_name' => 'major',
        'field_value' => '我是需求专业新文本',
        'type' => 'job'
    ]
];

echo "配置数据:\n";
print_r($configs);

// 应用配置
$modifiedData = \common\base\models\BaseSpecialNeedConfig::applyConfigs($h5Data, $configs, 'job');

echo "\n应用配置后的数据:\n";
print_r($modifiedData);

// 验证结果
echo "\n=== 验证结果 ===\n";

if (isset($modifiedData['majorTxt']) && $modifiedData['majorTxt'] === '我是需求专业新文本') {
    echo "✅ majorTxt字段更新正确: {$modifiedData['majorTxt']}\n";
} else {
    echo "❌ majorTxt字段更新失败\n";
}

if (isset($modifiedData['major']) && is_array($modifiedData['major']) && 
    isset($modifiedData['major'][0]['majorText']) && 
    $modifiedData['major'][0]['majorText'] === '我是需求专业新文本') {
    echo "✅ major数组字段同步正确: {$modifiedData['major'][0]['majorText']}\n";
} else {
    echo "❌ major数组字段同步失败\n";
    if (isset($modifiedData['major'][0]['majorText'])) {
        echo "   实际值: {$modifiedData['major'][0]['majorText']}\n";
    }
}

// 测试字段转换逻辑
echo "\n=== 测试字段转换逻辑 ===\n";
$actualField = \common\base\models\BaseSpecialNeedConfig::convertFieldName('major', $h5Data, 'job');
echo "major字段在H5数据中转换为: {$actualField}\n";
echo "说明: H5数据同时有major和majorTxt字段，优先选择majorTxt字段进行更新\n";

// 测试另一种数据结构（PC端可能的情况）
echo "\n=== 测试PC端数据结构 ===\n";
$pcData = [
    'id' => 456,
    'title' => '高级工程师',
    'major' => '120,121',  // PC端可能使用ID格式
    'education' => '硕士'
];

echo "原始PC数据:\n";
print_r($pcData);

$modifiedPcData = \common\base\models\BaseSpecialNeedConfig::applyConfigs($pcData, $configs, 'job');

echo "\n应用配置后的PC数据:\n";
print_r($modifiedPcData);

$actualFieldPc = \common\base\models\BaseSpecialNeedConfig::convertFieldName('major', $pcData, 'job');
echo "major字段在PC数据中转换为: {$actualFieldPc}\n";

// 测试追加功能
echo "\n=== 测试追加功能 ===\n";
$appendConfigs = [
    [
        'field_name' => 'major',
        'field_value' => ',计算机科学与技术',  // 以逗号开头表示追加
        'type' => 'job'
    ]
];

$appendedData = \common\base\models\BaseSpecialNeedConfig::applyConfigs($modifiedData, $appendConfigs, 'job');

echo "追加配置后的数据:\n";
print_r($appendedData);

echo "\n=== 总结 ===\n";
echo "1. 当配置应用到majorTxt字段时，major数组字段会被同步更新\n";
echo "2. 当配置应用到major字段时，majorTxt字段也会被同步更新\n";
echo "3. 系统会根据数据结构智能选择实际应用的字段\n";
echo "4. 支持文本追加功能（以逗号开头）\n";

echo "\n=== 测试完成 ===\n";
