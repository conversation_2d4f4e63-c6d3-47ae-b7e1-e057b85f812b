<?php

namespace boShiHou\controllers;

use common\base\models\BaseAnnouncementExtra;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyStatData;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJobExtra;
use common\base\models\BaseShowcase;
use common\service\boShiHouColumn\CompanyService;
use Yii;
use yii\db\conditions\AndCondition;

class CompanyController extends BaseBoShiHouController
{
    /**
     * 招收单位&PI大厅
     * 需求版本：v2.3
     */
    public function actionIndex()
    {
        $data = (new CompanyService())->getHomeIndexByCache(Yii::$app->request->get('isCache', 0));
        $list = $this->renderPartial('company_index_item.html', [
            'list' => $data['list']['list'],
        ]);
        $this->setSeo(Yii::$app->params['tdk']['company']);

        return $this->render('index.html', [
            'data' => $data['data'],
            'list' => [
                'list'  => $list,
                'limit' => $data['list']['limit'],
                'count' => $data['list']['count'],
            ],
        ]);
    }

    /**
     * 查询单位列表
     *  需求版本：v2.3
     */
    public function actionGetSearchList()
    {
        $searchData = Yii::$app->request->get();
        $data       = (new CompanyService())->searchList($searchData);
        $list       = $this->renderPartial('company_index_item.html', [
            'list' => $data['list'],
        ]);

        return $this->success([
            'list'  => $list,
            'limit' => $data['limit'],
            'count' => $data['count'],
        ]);
    }

    public function actionGetSearchParams()
    {
        return $this->success((new CompanyService())->getSearchParams());
    }
}