<?php

namespace miniApp\models;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseCommon;
use common\base\models\BaseJob;
use common\libs\Cache;
use common\service\search\CommonSearchApplication;

class Announcement extends BaseAnnouncement
{
    /**
     * 检索结果页公告检索
     * @throws \Exception
     */
    public static function searchForAnnouncementList($keyWords): array
    {
        $app = CommonSearchApplication::getInstance();

        return $app->getMiniAnnouncementSearch($keyWords);
    }

    /**
     * 公告详情
     * @throws \Exception
     */
    public static function getMiniAnnouncementDetail($keyWords): array
    {
        return BaseAnnouncement::getMiniAnnouncementDetail($keyWords);
    }

    /**
     * 获取详情信息，嵌套自base类，base类里的getDetailService目前只有这里使用，怕影响其他地方
     * @param $id
     * @param $platform
     * @param $memberId
     * @param $isDelCache 是否清除缓存重新设置
     * @return array|mixed
     * @throws \yii\base\Exception
     */
    public static function getDetailService($id, $platform, $memberId = 0, $isDelCache = false)
    {
        $cacheKey = Cache::MINI_ANNOUNCEMENT_DETAIL_KEY . ':' . $id;
        //需要清空缓存重新设置
        if ($isDelCache) {
            Cache::delete($cacheKey);
        }
        $cacheData = Cache::get($cacheKey);
        if ($cacheData) {
            return json_decode($cacheData, true);
        } else {
            $detailInfo = BaseAnnouncement::getDetailService($id, $platform, $memberId);
            //设置下缓存
            Cache::set($cacheKey, json_encode($detailInfo), self::DETAIL_CACHE_TIME);

            return $detailInfo;
        }
    }
}