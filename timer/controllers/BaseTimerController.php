<?php

namespace timer\controllers;

use common\service\resume\GetEditInfoService;
use yii\console\Controller;
use yii;

/**
 * 基础的定时任务
 */
class BaseTimerController extends Controller
{

    private static $actionName;

    public static $count = 0;

    // 脚本开始执行
    public function beforeAction($action)
    {
        date_default_timezone_set('Asia/Shanghai');

        // 关闭内存限制
        ini_set('memory_limit', -1);
        // 关闭超时限制
        set_time_limit(0);
        self::$actionName = $action->uniqueId;
        $str              = ' 开始执行 控制器: ' . self::$actionName . ' 时间: ' . CUR_DATETIME;

        self::log($str);

        return parent::beforeAction($action);
    }

    // 脚本结束执行
    public function afterAction($action, $result)
    {
        self::$actionName = $action->uniqueId;
        $time_len         = microtime(true) - CUR_MICRO_TIME;
        $str              = ' 结束执行 控制器: ' . self::$actionName . ' 时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $str              .= ' 总时长 ' . $time_len;

        self::log($str);

        return parent::afterAction($action, $result);
    }

    /**
     * 获取执行时长
     *
     * @param float $time_len 时长,单位:秒
     *
     * @return string
     */
    protected static function getRunTimeLenStr(float $time_len): string
    {
        if (0 == $time_len) {
            return '0s';
        }

        $hours   = floor($time_len / 3600);
        $minutes = floor(($time_len - $hours * 3600) / 60);
        $seconds = $time_len - $hours * 3600 - $minutes * 60;

        $time_str = '';
        if ($hours > 0) {
            $time_str .= $hours . 'h ';
        }
        if ($minutes > 0) {
            $time_str .= $minutes . 'min ';
        }
        if ($seconds > 0) {
            $time_str .= $seconds . 's ';
        }

        return $time_str;
    }

    /**
     * 脚本运行成功
     */
    public function okExecute()
    {
        $time_len = microtime(true) - CUR_MICRO_TIME;
        $str      = self::$actionName . ' RunTimeLen:' . self::getRunTimeLenStr($time_len) . PHP_EOL;

        exit($str);
    }

    /**
     * 对于内存和执行时间做一下修改
     * @param string $memory_limit
     * @param int    $time_limit
     */
    public static function durable(string $memory_limit = '1024m', int $time_limit = 0)
    {
        ini_set('memory_limit', $memory_limit);
        set_time_limit($time_limit);
    }

    /**
     * 输出内容
     * @param $string
     */
    public static function output($string)
    {
        var_export($string, true);
    }

    public static function getPercentText($num)
    {
        $count   = self::$count;
        $percent = $count == 0 ? 0 : round($num / $count * 100, 2);

        return "已完成{$num}/{$count}个,百分比:{$percent}%";
    }

    public static function log($str)
    {
        $logPath = Yii::getAlias('@timer/runtime/logs/timer');

        if (!is_dir($logPath)) {
            mkdir($logPath, 0777, true);
        }

        $dateLogPath = $logPath . '/' . CUR_DATE;

        if (!is_dir($dateLogPath)) {
            mkdir($dateLogPath, 0777, true);
        }

        $fileName = $dateLogPath . '/' . str_replace('/', '_', self::$actionName) . '_' . CUR_TIMESTAMP . '.log';

        if (!file_exists($fileName)) {
            touch($fileName, 0777, true);
        }

        // 输出到毫秒
        $str = '时间:' . date('Y-m-d H:i:s') . ' ' . $str;

        echo $str . PHP_EOL;

        file_put_contents($fileName, $str . PHP_EOL, FILE_APPEND);
    }


    /**
     * @param        $error_code
     * @param string $error_msg
     *
     * @throws \Exception
     */
    //    protected function throwException($error_code, $error_msg = '')
    //    {
    //        if (is_array($error_msg)) {
    //            $msg_arr   = $error_msg;
    //            $error_msg = ErrorCode::getMessage($error_code);
    //            foreach ($msg_arr as $search => $msg) {
    //                $error_msg = str_replace(':' . $search, $msg, $error_msg);
    //            }
    //        } else {
    //            empty($error_msg) && $error_msg = ErrorCode::getMessage($error_code);
    //        }
    //        throw new \Exception($error_msg, $error_code);
    //    }

    //    /**
    //     * 根据异常信息输出错误信息
    //     *
    //     * @param \Exception $e
    //     */
    //    protected function getExceptionExecuteError(\Exception $e)
    //    {
    //        echo 'code:', $e->getCode(), PHP_EOL;
    //
    //        $message = $e->getMessage();
    //        $message = mb_strlen($message) > 200 ? mb_substr($message, 0, 200) : $message;
    //
    //        echo 'message:', $message, PHP_EOL;
    //        $trace = $e->getTraceAsString();
    //        $trace = explode('#', $trace);
    //        $trace = array_slice(array_reverse($trace), 7);
    //        $trace = array_filter($trace);
    //
    //        $trace_str = '';
    //        foreach ($trace as $val) {
    //            $trace_str .= '#' . $val;
    //        }
    //
    //        echo 'trace:', PHP_EOL, $trace_str, PHP_EOL;
    //    }

    /**
     * note: 原生sql批量更新封装
     * @param        $primaryKeyField            主键字段名
     * @param        $data                       更新的数据，二维数组
     * @param string $table                      表名
     * @param string $updateFieldValue           更新的字段，一维数组
     * @return mixed
     */
    public function batchUpdate($primaryKeyField, $data, $table, $updateFieldValue = '')
    {
        $updateFieldValue = empty($updateFieldValue) ? array_keys($data[0]) : $updateFieldValue;
        $ids              = implode(",", array_column($data, $primaryKeyField));
        $sqlHead          = "UPDATE `{$table}` SET " . ' ';
        $sqlEnd           = "WHERE {$primaryKeyField} in ({$ids})";
        $sqlMiddle        = '';

        foreach ($updateFieldValue as $k => $v) {
            $condition = '';
            foreach ($data as $key) {
                $condition .= "WHEN {$key[$primaryKeyField]} THEN '{$key[$v]}' ";
            }
            $sqlMiddle .= "{$v} = CASE {$primaryKeyField} {$condition} END,";
        }
        $sql = $sqlHead . rtrim($sqlMiddle, ',') . ' ' . $sqlEnd;
        $res = Yii::$app->db->createCommand($sql)
            ->execute();

        return $res;
    }

}
