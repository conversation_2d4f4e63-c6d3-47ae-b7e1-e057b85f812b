<?php

namespace admin\models;

use common\base\models\BaseSpecialNeedApplyLimit;
use yii\data\ActiveDataProvider;

/**
 * 投递限制管理模型
 */
class SpecialNeedApplyLimit extends BaseSpecialNeedApplyLimit
{
    /**
     * 获取投递限制列表
     * @param array $params
     * @return array
     */
    public static function getList($params)
    {
        $query = static::find();

        // 添加搜索条件
        $query->andFilterWhere([
            'id' => $params['id'] ?? null,
            'company_id' => $params['company_id'] ?? null,
            'limit_type' => $params['limit_type'] ?? null,
            'time_limit' => $params['time_limit'] ?? null,
            'count_limit' => $params['count_limit'] ?? null,
            'status' => $params['status'] ?? null,
            'created_by' => $params['created_by'] ?? null,
        ]);

        $query->andFilterWhere(['like', 'name', $params['name'] ?? null])
            ->andFilterWhere(['like', 'condition_field', $params['condition_field'] ?? null])
            ->andFilterWhere(['like', 'condition_value', $params['condition_value'] ?? null])
            ->andFilterWhere(['like', 'error_message', $params['error_message'] ?? null])
            ->andFilterWhere(['like', 'remark', $params['remark'] ?? null]);

        // 时间范围搜索
        if (!empty($params['add_time'])) {
            if (strpos($params['add_time'], ' - ') !== false) {
                list($start, $end) = explode(' - ', $params['add_time']);
                $query->andFilterWhere(['>=', 'add_time', $start . ' 00:00:00'])
                    ->andFilterWhere(['<=', 'add_time', $end . ' 23:59:59']);
            } else {
                $query->andFilterWhere(['like', 'add_time', $params['add_time']]);
            }
        }

        // 获取总数
        $count = $query->count();

        // 分页参数
        $pageSize = $params['pageSize'] ?? \Yii::$app->params['defaultPageSize'];
        $page = $params['page'] ?? 1;
        $pages = static::setPage($count, $page, $pageSize);

        // 获取数据
        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('id DESC')
            ->asArray()
            ->all();

        // 处理数据格式
        foreach ($list as &$item) {
            $item['limit_type_text'] = static::getLimitTypeOptions()[$item['limit_type']] ?? '未知';
            $item['status_text'] = static::getStatusOptions()[$item['status']] ?? '未知';
            $item['condition_field_text'] = static::getConditionFieldTextStatic($item['condition_field']);
            $item['limit_description'] = static::getLimitDescriptionStatic($item);
            $item['company_info'] = "单位ID: {$item['company_id']}";
            $item['created_by_name'] = "用户{$item['created_by']}";
        }

        return [
            'list' => $list,
            'pages' => [
                'total' => (int)$count,
                'limit' => (int)$pageSize,
                'page' => (int)$page,
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return array_merge(parent::rules(), [
            [['add_time'], 'safe'], // 允许搜索时间范围
        ]);
    }

    /**
     * 保存前处理
     */
    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            if ($insert) {
                $this->created_by = \Yii::$app->user->id ?? 0;
            }
            return true;
        }
        return false;
    }

    /**
     * 获取创建人姓名
     * @return string
     */
    public function getCreatedByName()
    {
        if ($this->created_by) {
            // 这里可以关联用户表获取用户名
            return "用户{$this->created_by}";
        }
        return '系统';
    }

    /**
     * 获取状态文本
     * @return string
     */
    public function getStatusText()
    {
        $options = static::getStatusOptions();
        return $options[$this->status] ?? '未知';
    }

    /**
     * 获取限制类型文本
     * @return string
     */
    public function getLimitTypeText()
    {
        $options = static::getLimitTypeOptions();
        return $options[$this->limit_type] ?? '未知';
    }

    /**
     * 获取条件字段文本
     * @return string
     */
    public function getConditionFieldText()
    {
        if (empty($this->condition_field)) {
            return '-';
        }
        
        $options = static::getConditionFieldOptions();
        return $options[$this->condition_field] ?? $this->condition_field;
    }

    /**
     * 获取限制描述
     * @return string
     */
    public function getLimitDescription()
    {
        switch ($this->limit_type) {
            case static::LIMIT_TYPE_COUNT:
                if ($this->time_limit > 0) {
                    return "{$this->time_limit}天内最多投递{$this->count_limit}次";
                } else {
                    return "总共最多投递{$this->count_limit}次";
                }
                
            case static::LIMIT_TYPE_CONDITION:
                return "条件限制：{$this->getConditionFieldText()} = {$this->condition_value}";
                
            default:
                return '未知限制类型';
        }
    }

    /**
     * 获取单位信息
     * @return string
     */
    public function getCompanyInfo()
    {
        // 这里可以关联单位表获取单位名称
        return "单位ID: {$this->company_id}";
    }

    /**
     * 验证规则扩展
     */
    public function validateLimitConfig()
    {
        if ($this->limit_type == static::LIMIT_TYPE_COUNT) {
            if ($this->count_limit <= 0) {
                $this->addError('count_limit', '次数限制必须大于0');
            }
        }
        
        if ($this->limit_type == static::LIMIT_TYPE_CONDITION) {
            if (empty($this->condition_field)) {
                $this->addError('condition_field', '条件字段不能为空');
            }
            if (empty($this->condition_value)) {
                $this->addError('condition_value', '条件值不能为空');
            }
        }
    }

    /**
     * 检查是否有效
     * @return bool
     */
    public function isValid()
    {
        return $this->status == static::STATUS_ENABLED;
    }

    /**
     * 获取条件字段文本（静态方法）
     * @param string $conditionField
     * @return string
     */
    public static function getConditionFieldTextStatic($conditionField)
    {
        if (empty($conditionField)) {
            return '-';
        }

        $options = static::getConditionFieldOptions();
        return $options[$conditionField] ?? $conditionField;
    }

    /**
     * 获取限制描述（静态方法）
     * @param array $item
     * @return string
     */
    public static function getLimitDescriptionStatic($item)
    {
        switch ($item['limit_type']) {
            case static::LIMIT_TYPE_COUNT:
                if ($item['time_limit'] > 0) {
                    return "{$item['time_limit']}天内最多投递{$item['count_limit']}次";
                } else {
                    return "总共最多投递{$item['count_limit']}次";
                }

            case static::LIMIT_TYPE_CONDITION:
                $fieldText = static::getConditionFieldTextStatic($item['condition_field']);
                return "条件限制：{$fieldText} = {$item['condition_value']}";

            default:
                return '未知限制类型';
        }
    }
}
