<?php

namespace admin\models;

use common\base\models\BaseSpecialNeedConfig;
use yii\data\ActiveDataProvider;

/**
 * 特殊需求配置管理模型
 */
class SpecialNeedConfig extends BaseSpecialNeedConfig
{
    /**
     * 获取配置列表
     * @param array $params
     * @return array
     */
    public static function getList($params)
    {
        $query = static::find();

        // 添加搜索条件
        $query->andFilterWhere([
            'id'         => $params['id'] ?? null,
            'type'       => $params['type'] ?? null,
            'target_id'  => $params['target_id'] ?? null,
            'status'     => $params['status'] ?? null,
            'created_by' => $params['created_by'] ?? null,
        ]);

        $query->andFilterWhere([
            'like',
            'name',
            $params['name'] ?? null,
        ])
            ->andFilterWhere([
                'like',
                'field_name',
                $params['field_name'] ?? null,
            ])
            ->andFilterWhere([
                'like',
                'platform',
                $params['platform'] ?? null,
            ])
            ->andFilterWhere([
                'like',
                'remark',
                $params['remark'] ?? null,
            ]);

        // 时间范围搜索
        if (!empty($params['add_time'])) {
            if (strpos($params['add_time'], ' - ') !== false) {
                [
                    $start,
                    $end,
                ] = explode(' - ', $params['add_time']);
                $query->andFilterWhere([
                    '>=',
                    'add_time',
                    $start . ' 00:00:00',
                ])
                    ->andFilterWhere([
                        '<=',
                        'add_time',
                        $end . ' 23:59:59',
                    ]);
            } else {
                $query->andFilterWhere([
                    'like',
                    'add_time',
                    $params['add_time'],
                ]);
            }
        }

        // 获取总数
        $count = $query->count();

        // 分页参数
        $pageSize = $params['pageSize'] ?? \Yii::$app->params['defaultPageSize'];
        $page     = $params['page'] ?? 1;
        $pages    = static::setPage($count, $page, $pageSize);

        // 获取数据
        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('id DESC')
            ->asArray()
            ->all();

        // 处理数据格式
        foreach ($list as &$item) {
            $item['type_text']          = static::getTypeOptions()[$item['type']] ?? '未知';
            $item['platform_text']      = static::getPlatformOptions()[$item['platform']] ?? '未知';
            $item['status_text']        = static::getStatusOptions()[$item['status']] ?? '未知';
            $item['field_text']         = static::getFieldDisplayName($item['field_name'], $item['type']);
            $item['target_info']        = static::getTargetInfoStatic($item['type'], $item['target_id']);
            $item['active_status_text'] = static::getActiveStatusTextStatic($item);
            $item['created_by_name']    = "用户{$item['created_by']}";
        }

        return [
            'list'  => $list,
            'pages' => [
                'total' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$page,
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return array_merge(parent::rules(), [
            [
                ['add_time'],
                'safe',
            ],
            // 允许搜索时间范围
        ]);
    }

    /**
     * 保存前处理
     */
    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            if ($insert) {
                $this->created_by = \Yii::$app->user->id ?? 0;
            }

            return true;
        }

        return false;
    }

    /**
     * 获取创建人姓名
     * @return string
     */
    public function getCreatedByName()
    {
        if ($this->created_by) {
            // 这里可以关联用户表获取用户名
            return "用户{$this->created_by}";
        }

        return '系统';
    }

    /**
     * 获取状态文本
     * @return string
     */
    public function getStatusText()
    {
        $options = static::getStatusOptions();

        return $options[$this->status] ?? '未知';
    }

    /**
     * 获取类型文本
     * @return string
     */
    public function getTypeText()
    {
        $options = static::getTypeOptions();

        return $options[$this->type] ?? '未知';
    }

    /**
     * 获取平台文本
     * @return string
     */
    public function getPlatformText()
    {
        $options = static::getPlatformOptions();

        return $options[$this->platform] ?? '未知';
    }

    /**
     * 获取字段文本
     * @return string
     */
    public function getFieldText()
    {
        $options = static::getFieldOptions($this->type);

        return $options[$this->field_name] ?? $this->field_name;
    }

    /**
     * 检查是否生效中
     * @return bool
     */
    public function isActive()
    {
        if ($this->status != static::STATUS_ENABLED) {
            return false;
        }

        $now = date('Y-m-d H:i:s');

        if ($this->start_time && $this->start_time > $now) {
            return false;
        }

        if ($this->end_time && $this->end_time < $now) {
            return false;
        }

        return true;
    }

    /**
     * 获取生效状态文本
     * @return string
     */
    public function getActiveStatusText()
    {
        if (!$this->isActive()) {
            if ($this->status != static::STATUS_ENABLED) {
                return '已禁用';
            }

            $now = date('Y-m-d H:i:s');
            if ($this->start_time && $this->start_time > $now) {
                return '未开始';
            }

            if ($this->end_time && $this->end_time < $now) {
                return '已过期';
            }
        }

        return '生效中';
    }

    /**
     * 获取目标信息
     * @return string
     */
    public function getTargetInfo()
    {
        switch ($this->type) {
            case static::TYPE_JOB:
                return "职位ID: {$this->target_id}";
            case static::TYPE_ANNOUNCEMENT:
                return "公告ID: {$this->target_id}";
            case static::TYPE_COMPANY:
                return "单位ID: {$this->target_id}";
            default:
                return "ID: {$this->target_id}";
        }
    }

    /**
     * 验证规则扩展
     */
    public function validateFieldValue()
    {
        // 可以在这里添加字段值的验证逻辑
        // 比如检查JSON格式、长度限制等
        if (strlen($this->field_value) > 5000) {
            $this->addError('field_value', '字段值长度不能超过5000个字符');
        }
    }

    /**
     * 获取目标信息（静态方法）
     * @param string $type
     * @param int    $targetId
     * @return string
     */
    public static function getTargetInfoStatic($type, $targetId)
    {
        switch ($type) {
            case static::TYPE_JOB:
                return "职位ID: {$targetId}";
            case static::TYPE_ANNOUNCEMENT:
                return "公告ID: {$targetId}";
            case static::TYPE_COMPANY:
                return "单位ID: {$targetId}";
            default:
                return "ID: {$targetId}";
        }
    }

    /**
     * 获取生效状态文本（静态方法）
     * @param array $item
     * @return string
     */
    public static function getActiveStatusTextStatic($item)
    {
        if ($item['status'] != static::STATUS_ENABLED) {
            return '已禁用';
        }

        $now = date('Y-m-d H:i:s');
        if ($item['start_time'] && $item['start_time'] > $now) {
            return '未开始';
        }

        if ($item['end_time'] && $item['end_time'] < $now) {
            return '已过期';
        }

        return '生效中';
    }
}
