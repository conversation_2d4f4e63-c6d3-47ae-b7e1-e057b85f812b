<?php

namespace admin\models;

use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseMajorAiDictionary;
use common\base\models\BaseMajorAiDictionaryRelationship;
use common\base\models\BaseMajorAiDictionarySynonym;
use common\base\models\BaseMajorAiLog;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\models\MajorAiLog;
use Yii;

class MajorAi extends BaseMajorAiDictionary
{
    /**
     * 这个是从学科的角度出发
     * @param $params
     * @return array
     */
    public static function getList($params = [])
    {
        // 首先是找到所有的学科
        $list = Major::find()
            ->select([
                'id as k',
                'name as v',
                'parent_id as parentId',
                'code',
                'level',
            ])
            ->where(['status' => 1])
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            if ($item['level'] == 2) {
                // 去找有没有匹配的
                $item['aiText']    = self::find()
                    ->alias('a')
                    ->innerJoin(['b' => BaseMajorAiDictionaryRelationship::tableName()], 'a.id=b.dictionary_id')
                    ->select('a.name')
                    ->where(['major_id' => $item['k']])
                    ->column();
                $item['aiTextTxt'] = implode(',', $item['aiText']);
            }
        }

        return ArrayHelper::objMoreArr($list);
    }

    /**
     * 这个是从字典的角度出发
     * @param $params
     * @return array
     */
    public static function getAiTextList($params = [])
    {
        $isExport = $params['export'] == 1 ? true : false;

        $query = self::find()
            ->alias('a');

        // 这里加入同义词以后，还有一个新逻辑，就是如果有name，就要去同义词表里面先找一次
        if ($params['name']) {
            $idList = BaseMajorAiDictionarySynonym::find()
                ->select('major_ai_dictionary_id')
                ->where([
                    'like',
                    'name',
                    $params['name'],
                ])
                ->column();

            $query->andWhere([
                'or',
                [
                    'like',
                    'name',
                    $params['name'],
                ],
                ['id' => $idList],
            ]);
        }

        if ($isExport) {
            $list = $query->select('a.name,a.id')
                ->orderBy('id desc')
                ->asArray()
                ->all();
        } else {
            $count    = $query->count();
            $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
            $pages    = self::setPage($count, $params['page'], $pageSize);

            $list = $query->select('a.name,a.id')
                ->offset($pages['offset'])
                ->limit($pages['limit'])
                ->orderBy('id desc')
                ->asArray()
                ->all();
        }

        // 递归去找对应的学科信息
        foreach ($list as &$item) {
            $item['majorList'] = BaseMajorAiDictionaryRelationship::find()
                ->alias('r')
                ->select([
                    'm.id',
                    'm.name',
                ])
                ->innerJoin(['m' => Major::tableName()], 'r.major_id=m.id')
                ->where(['r.dictionary_id' => $item['id']])
                ->asArray()
                ->all();

            $item['majorName'] = implode(',', ArrayHelper::getColumn($item['majorList'], 'name'));

            $item['synonym'] = BaseMajorAiDictionarySynonym::find()
                ->select('name')
                ->where(['major_ai_dictionary_id' => $item['id']])
                ->asArray()
                ->column();
        }

        if ($isExport) {
            $headers = [
                '字典',
                '同义词',
                '识别学科',
            ];
            $data    = [];
            foreach ($list as $excelData) {
                $data[] = [
                    $excelData['name'],
                    implode(',', $excelData['synonym']),
                    $excelData['majorName'],
                ];
            }

            return [
                'headers'  => $headers,
                'data'     => $data,
                'fileName' => BaseAdminDownloadTask::TYPE_COMPANY_GROUP_LIST_NAME . date('YmdHis'),
            ];
        }

        return [
            'list'  => $list,
            'pages' => [
                'size'  => (int)$pageSize,
                'total' => (int)$count,
            ],
        ];
    }

    public static function getAiTextDetail($id)
    {
        $detail = self::find()
            ->select('id,name')
            ->where([
                'id'     => $id,
                'status' => self::STATUS_ACTIVE,
            ])
            ->asArray()
            ->one();

        // 找到关系里面的学科信息
        $detail['majorList'] = BaseMajorAiDictionaryRelationship::find()
            ->alias('r')
            ->select([
                'm.id',
                'm.name',
            ])
            ->innerJoin(['m' => Major::tableName()], 'r.major_id=m.id')
            ->where(['r.dictionary_id' => $detail['id']])
            ->asArray()
            ->all();

        return $detail;
    }

    /**
     * 新增和编辑都在这里
     * @param $data
     *
     */
    public static function addAiText($data)
    {
        $id       = trim($data['id']);
        $name     = trim($data['name']);
        $majorIds = trim($data['majorIds']);

        // 检查合法性
        if (!$name) {
            throw new \Exception('识别文本不能为空');
        }

        if (!$majorIds) {
            throw new \Exception('请选择学科');
        }

        // 用名字去检查重复

        $model       = $id ? self::findOne($id) : new self();
        $model->name = $name;
        if (!$model->save()) {
            throw new \Exception($model->getFirstErrorsMessage());
        }

        // 判断是否和同义词重复
        $synonymList = BaseMajorAiDictionarySynonym::find()
            ->select('name')
            ->where(['name' => $name])
            ->exists();

        if ($synonymList) {
            throw new \Exception($name . '在同义词中已经存在');
        }

        $id = $model->id;

        if (!$id) {
            throw new \Exception('关联失败');
        }
        // 这个时候去删除之前的关联?
        BaseMajorAiDictionaryRelationship::deleteAll(['dictionary_id' => $id]);

        $majorArray = explode(',', $majorIds);
        if (count($majorArray) < 1) {
            throw new \Exception('学科参数非法');
        }

        // 巡检去检查并添加?
        foreach ($majorArray as $item) {
            $majorId = $item;
            $major   = Major::findOne($item);
            if ($major->status != 1) {
                throw new \Exception($major->name . '已被删除,无法关联');
            }
            if ($major->level != 2) {
                throw new \Exception($major->name . '不是二级,无法关联');
            }
            $relModel                = new BaseMajorAiDictionaryRelationship();
            $relModel->dictionary_id = $id;
            $relModel->major_id      = $majorId;
            if (!$relModel->save()) {
                throw new \Exception($relModel->getFirstErrorsMessage());
            }
        }

        self::getCache(true);
    }

    /**
     * @param $text
     *
     */
    public static function recognition($text, $type)
    {
        $textList = self::getCache();

        // 识别
        $ids             = [];
        $names           = [];
        $dictionaryIds   = [];
        $dictionaryNames = [];
        $fullText        = $text;
        // 把textList按照长度排序
        uasort($textList, function ($a, $b) {
            return mb_strlen($a['name']) < mb_strlen($b['name']);
        });

        foreach ($textList as $item) {
            $name = $item['name'];
            // 找text里面是否有包含name
            if (strstr($text, $name)) {
                // 找到了字典,这个时候去找匹配的majorId
                $majorList = BaseMajorAiDictionaryRelationship::find()
                    ->alias('a')
                    ->select('m.id,m.name')
                    ->innerJoin(['m' => Major::tableName()], 'm.id=a.major_id')
                    ->where(['dictionary_id' => $item['id']])
                    ->asArray()
                    ->all();

                $majorIds          = ArrayHelper::getColumn($majorList, 'id');
                $majorNames        = ArrayHelper::getColumn($majorList, 'name');
                $dictionaryNames[] = $name;
                $dictionaryIds[]   = $item['id'];

                $ids   = array_merge($majorIds, $ids);
                $names = array_merge($majorNames, $names);

                // 如果匹配成功就在text里面删除掉name
                $text = str_replace($name, '', $text);
            }
        }

        $ids   = array_unique($ids);
        $names = array_unique($names);

        if (count($ids) > 0) {
            // 识别成功,写一条识别日志
            $model                   = new MajorAiLog();
            $model->text             = $fullText;
            $model->type             = $type;
            $model->dictionary_ids   = implode(',', $dictionaryIds);
            $model->dictionary_names = json_encode($dictionaryNames);
            $model->major_ids        = implode(',', $ids);
            $model->major_names      = implode(',', $names);
            if (!$model->save()) {
                throw new \Exception($model->getFirstErrorsMessage());
            }
        }

        // 重新排序从0开始

        return [
            'majorIds'   => array_values($ids),
            'majorNames' => array_values($names),
        ];
    }

    public static function recognitionLogList($params)
    {
        $query = BaseMajorAiLog::find();

        $query->andFilterWhere([
            'like',
            'text',
            $params['text'],
        ]);
        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);

        $list = $query->select('text,type,dictionary_names,major_names,add_time')
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('id desc')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['typeTxt']          = BaseMajorAiLog::TYPE_LIST[$item['type']];
            $item['dictionary_names'] = json_decode($item['dictionary_names'], true);
        }

        return [
            'list'  => $list,
            'pages' => [
                'size'  => (int)$pageSize,
                'total' => (int)$count,
            ],
        ];
    }

    // 修改同义词
    public static function editSynonym($id, $synonym)
    {
        $model = self::findOne($id);
        if (!$model) {
            throw new \Exception('数据不存在');
        }

        // 找到这个id的同义词
        // $list = BaseMajorAiDictionarySynonym::find()
        //     ->where(['major_ai_dictionary_id' => $id])
        //     ->asArray()
        //     ->all();

        // 删除BaseMajorAiDictionarySynonym
        BaseMajorAiDictionarySynonym::deleteAll(['major_ai_dictionary_id' => $id]);

        // 找是否有重复的，避免重复添加了
        foreach ($synonym as $item) {
            if ($model->name == $item) {
                throw new MessageException($item . '不能和字典名字一样');
            }

            if (self::find()
                ->where([
                    'name'   => $item,
                    'status' => self::STATUS_ACTIVE,
                ])
                ->andWhere([
                    '<>',
                    'id',
                    $id,
                ])
                ->exists()) {
                throw new MessageException($item . '已经在字典中存在了');
            }

            if (BaseMajorAiDictionarySynonym::find()
                ->where([
                    'name'   => $item,
                    'status' => self::STATUS_ACTIVE,
                ])
                ->andWhere([
                    '<>',
                    'major_ai_dictionary_id',
                    $id,
                ])
                ->exists()) {
                throw new MessageException($item . '已经在别的同义词中存在了');
            }
        }

        // 全量写入
        $data = [];

        foreach ($synonym as $item) {
            $data[] = [
                CUR_DATETIME,
                $id,
                $item,
                self::STATUS_ACTIVE,
            ];
        }

        BaseMajorAiDictionarySynonym::getDb()
            ->createCommand()
            ->batchInsert(BaseMajorAiDictionarySynonym::tableName(), [
                'add_time',
                'major_ai_dictionary_id',
                'name',
                'status',
            ], $data)
            ->execute();

        self::getCache(true);

        return true;
    }

    public static function deleteAiText($id)
    {
        // 直接删除数据，不用保留
        self::deleteAll(['id' => $id]);

        BaseMajorAiDictionarySynonym::deleteAll(['major_ai_dictionary_id' => $id]);

        self::getCache(true);
    }
}
