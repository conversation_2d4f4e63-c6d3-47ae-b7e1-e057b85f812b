<?php
/**
 * create user：shannon
 * create time：2024/4/2 14:05
 */
namespace admin\controllers;

use admin\models\ResumeEquityAdminSetting;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseResumeEquityAdminSetting;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use common\base\models\BaseResumeEquityPackageSetting;
use common\base\models\BaseResumeEquitySetting;
use common\helpers\ArrayHelper;
use common\service\downloadTask\DownLoadTaskApplication;
use Yii;
use yii\base\Exception;

class ResumeEquityAdminSettingController extends BaseAdminController
{
    /**
     * 获取列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionList()
    {
        $params = Yii::$app->request->get();

        return $this->success(ResumeEquityAdminSetting::list($params));
    }

    /**
     * 导出列表
     */
    public function actionExport()
    {
        $params  = Yii::$app->request->get();
        $adminId = Yii::$app->user->id;
        $app     = DownLoadTaskApplication::getInstance();
        $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_RESUME_EQUITY_ADMIN_SETTING, $params);

        return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
    }

    /**
     * 筛选项
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionFilter()
    {
        $resumeEquityPackage = BaseResumeEquityPackageSetting::find()
            ->where([
                'status'                     => BaseResumeEquityPackageSetting::STATUS_ONLINE,
                'equity_package_type'        => BaseResumeEquityPackageSetting::EQUITY_PACKAGE_TYPE_BASICS,
                'equity_package_category_id' => BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
            ])
            ->asArray()
            ->all();
        $data                = [
            'auditStatusList'     => ArrayHelper::obj2Arr(BaseResumeEquityAdminSetting::STATUS_AUDIT_LIST),
            'refundList'          => ArrayHelper::obj2Arr(BaseResumeEquityAdminSetting::IS_REFUND_LIST),
            'resumeEquityPackage' => ArrayHelper::obj2Arr(array_column($resumeEquityPackage, 'name', 'id')),
        ];

        return $this->success($data);
    }

    /**
     * 验证手机号是否注册
     */
    public function actionCheck()
    {
        try {
            $params = Yii::$app->request->post();
            $result = ResumeEquityAdminSetting::check($params);

            return $this->success($result);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 添加套餐配置
     */
    public function actionAdd()
    {
        try {
            $params = Yii::$app->request->post();
            $result = ResumeEquityAdminSetting::add($params);

            if ($result) {
                return $this->success($result, '配置成功');
            } else {
                return $this->fail('配置失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 查看与编辑初始化
     */
    public function actionInit()
    {
        try {
            $id = Yii::$app->request->get('id');

            return $this->success(ResumeEquityAdminSetting::init($id));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑套餐配置
     */
    public function actionEdit()
    {
        try {
            $params = Yii::$app->request->post();
            $result = ResumeEquityAdminSetting::edit($params);

            if ($result) {
                return $this->success($result, '配置编辑成功');
            } else {
                return $this->fail('配置编辑失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 查看编辑
     */
    public function actionViewEdit()
    {
        try {
            $params = Yii::$app->request->post();
            $res    = ResumeEquityAdminSetting::viewEdit($params);

            if ($res) {
                return $this->success($res, '查看编辑成功');
            } else {
                return $this->fail('查看编辑失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除套餐配置
     */
    public function actionDelete()
    {
        try {
            $id     = Yii::$app->request->get('id');
            $result = ResumeEquityAdminSetting::delete($id);

            if ($result) {
                return $this->success($result, '删除成功');
            } else {
                return $this->fail('删除失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 配置审核
     */
    public function actionSettingAudit()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $params = Yii::$app->request->post();
            //开启事务
            $result = ResumeEquityAdminSetting::settingAudit($params);

            if ($result) {
                $transaction->commit();

                return $this->success($result, '审核成功');
            } else {
                $transaction->rollBack();

                return $this->fail('审核失败');
            }
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 退款初始化
     */
    public function actionRefundInit()
    {
        try {
            $id = Yii::$app->request->get('id');

            return $this->success(ResumeEquityAdminSetting::refundInit($id));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 退款审核提交
     */
    public function actionRefundSubmit()
    {
        try {
            $params = Yii::$app->request->post();
            $result = ResumeEquityAdminSetting::refundSubmit($params);

            if ($result) {
                return $this->success($result, '提交成功');
            } else {
                return $this->fail('提交失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 退款审核
     */
    public function actionRefundAudit()
    {
        try {
            $params = Yii::$app->request->post();
            $result = ResumeEquityAdminSetting::refundAudit($params);

            if ($result) {
                return $this->success($result, '审核成功');
            } else {
                return $this->fail('审核失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}