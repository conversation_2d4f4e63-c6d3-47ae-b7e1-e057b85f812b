<?php

namespace admin\controllers;

use admin\models\SpecialNeedConfig;
use admin\models\SpecialNeedApplyLimit;
use common\base\models\BaseSpecialNeedConfig;
use common\base\models\BaseSpecialNeedApplyLimit;
use common\helpers\ArrayHelper;
use common\helpers\FormatConverter;
use Yii;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;

/**
 * 特殊需求管理控制器
 */
class SpecialNeedController extends BaseAdminController
{
    /**
     * 配置列表
     */
    public function actionConfigIndex()
    {
        try {
            $params = Yii::$app->request->queryParams;
            $result = SpecialNeedConfig::getList($params);

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 查看配置详情
     */
    public function actionConfigView($id)
    {
        try {
            $model = $this->findConfigModel($id);

            return $this->success($model->toArray());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 创建配置
     */
    public function actionConfigCreate()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $model = new SpecialNeedConfig();

            if ($model->load(Yii::$app->request->post()) && $model->save()) {
                $transaction->commit();

                return $this->success(['id' => $model->id], '配置创建成功');
            }

            $transaction->rollBack();

            return $this->fail('配置创建失败：' . implode(', ', $model->getFirstErrors()));
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新配置
     */
    public function actionConfigUpdate($id)
    {
        $transaction = Yii::$app->db->beginTransaction();
        $data = FormatConverter::convertHump(Yii::$app->request->post());
        try {
            $model = $this->findConfigModel($id);

            if ($model->load($data) && $model->save()) {
                $transaction->commit();

                return $this->success([], '配置更新成功');
            }

            $transaction->rollBack();

            return $this->fail('配置更新失败：' . implode(', ', $model->getFirstErrors()));
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除配置
     */
    public function actionConfigDelete($id)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $model = $this->findConfigModel($id);
            if ($model->delete()) {
                $transaction->commit();

                return $this->success([], '配置删除成功');
            }

            $transaction->rollBack();

            return $this->fail('配置删除失败');
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量启用/禁用配置
     */
    public function actionConfigBatchStatus()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $ids    = Yii::$app->request->post('ids', []);
            $status = Yii::$app->request->post('status', 1);

            if (empty($ids)) {
                return $this->fail('请选择要操作的配置');
            }

            $count = BaseSpecialNeedConfig::updateAll(['status' => $status], ['id' => $ids]);
            $transaction->commit();

            return $this->success(['count' => $count], "成功更新 {$count} 条配置状态");
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取字段选项（AJAX）
     */
    public function actionGetFieldOptions()
    {
        try {
            $type    = Yii::$app->request->get('type');
            $options = BaseSpecialNeedConfig::getFieldOptions($type);

            return $this->success($options);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 投递限制列表
     */
    public function actionLimitIndex()
    {
        try {
            $params = Yii::$app->request->queryParams;
            $result = SpecialNeedApplyLimit::getList($params);

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 查看投递限制详情
     */
    public function actionLimitView($id)
    {
        try {
            $model = $this->findLimitModel($id);

            return $this->success($model->toArray());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 创建投递限制
     */
    public function actionLimitCreate()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $model = new SpecialNeedApplyLimit();

            if ($model->load(Yii::$app->request->post()) && $model->save()) {
                $transaction->commit();

                return $this->success(['id' => $model->id], '投递限制创建成功');
            }

            $transaction->rollBack();

            return $this->fail('投递限制创建失败：' . implode(', ', $model->getFirstErrors()));
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新投递限制
     */
    public function actionLimitUpdate($id)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $model = $this->findLimitModel($id);

            if ($model->load(Yii::$app->request->post()) && $model->save()) {
                $transaction->commit();

                return $this->success([], '投递限制更新成功');
            }

            $transaction->rollBack();

            return $this->fail('投递限制更新失败：' . implode(', ', $model->getFirstErrors()));
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除投递限制
     */
    public function actionLimitDelete($id)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $model = $this->findLimitModel($id);
            if ($model->delete()) {
                $transaction->commit();

                return $this->success([], '投递限制删除成功');
            }

            $transaction->rollBack();

            return $this->fail('投递限制删除失败');
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 测试配置
     */
    public function actionTestConfig()
    {
        try {
            $type     = Yii::$app->request->post('type');
            $targetId = Yii::$app->request->post('target_id');
            $platform = Yii::$app->request->post('platform', 'PC');

            if (!$type || !$targetId) {
                return $this->fail('参数不完整');
            }

            $configs = BaseSpecialNeedConfig::getConfigs($type, $targetId, $platform);

            return $this->success($configs, '找到 ' . count($configs) . ' 条匹配的配置');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 导出配置
     */
    public function actionExportConfig()
    {
        $configs = BaseSpecialNeedConfig::find()
            ->asArray()
            ->all();

        $filename = 'special_need_config_' . date('Y-m-d_H-i-s') . '.json';

        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'application/json');
        Yii::$app->response->headers->add('Content-Disposition', "attachment; filename=\"{$filename}\"");

        return json_encode($configs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * 导入配置
     */
    public function actionImportConfig()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $uploadedFile = \yii\web\UploadedFile::getInstanceByName('config_file');

            if (!$uploadedFile) {
                return $this->fail('请选择要导入的文件');
            }

            $content = file_get_contents($uploadedFile->tempName);
            $configs = json_decode($content, true);

            if (!$configs) {
                return $this->fail('文件格式错误');
            }

            $count = 0;
            foreach ($configs as $configData) {
                unset($configData['id']); // 移除ID，让系统自动生成
                $configData['created_by'] = Yii::$app->user->id;

                $model = new BaseSpecialNeedConfig();
                if ($model->load($configData, '') && $model->save()) {
                    $count++;
                }
            }

            $transaction->commit();

            return $this->success(['count' => $count], "成功导入 {$count} 条配置");
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail('导入失败：' . $e->getMessage());
        }
    }

    /**
     * 查找配置模型
     */
    protected function findConfigModel($id)
    {
        if (($model = SpecialNeedConfig::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('请求的配置不存在');
    }

    /**
     * 查找投递限制模型
     */
    protected function findLimitModel($id)
    {
        if (($model = SpecialNeedApplyLimit::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('请求的投递限制不存在');
    }
}
