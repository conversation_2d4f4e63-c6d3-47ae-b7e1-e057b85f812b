<?php
/**
 * 测试特殊需求字段映射功能
 * 用于验证字段统一管理和转换功能是否正常工作
 */

require_once __DIR__ . '/common/base/models/BaseSpecialNeedConfig.php';

echo "=== 特殊需求字段映射功能测试 ===\n\n";

// 测试1: 获取统一后的字段选项
echo "1. 测试获取统一后的字段选项:\n";
$jobFields = \common\base\models\BaseSpecialNeedConfig::getFieldOptions('job');
echo "职位字段选项:\n";
foreach ($jobFields as $key => $label) {
    echo "  {$key} => {$label}\n";
}

$announcementFields = \common\base\models\BaseSpecialNeedConfig::getFieldOptions('announcement');
echo "\n公告字段选项:\n";
foreach ($announcementFields as $key => $label) {
    echo "  {$key} => {$label}\n";
}

$companyFields = \common\base\models\BaseSpecialNeedConfig::getFieldOptions('company');
echo "\n单位字段选项:\n";
foreach ($companyFields as $key => $label) {
    echo "  {$key} => {$label}\n";
}

// 测试2: 字段转换功能
echo "\n\n2. 测试字段转换功能:\n";

// 模拟职位数据（使用amount字段）
$jobData = [
    'id' => 123,
    'title' => '软件工程师',
    'amount' => 5,
    'education' => '本科',
    'major' => '计算机科学'
];

echo "原始职位数据:\n";
print_r($jobData);

// 测试转换recruitAmount字段
$actualField = \common\base\models\BaseSpecialNeedConfig::convertFieldName('recruitAmount', $jobData, 'job');
echo "recruitAmount字段在职位数据中转换为: {$actualField}\n";

// 模拟公告数据（使用recruitAmount字段）
$announcementData = [
    'id' => 456,
    'title' => '2024年招聘公告',
    'recruitAmount' => 10,
    'education' => '硕士',
    'major' => '软件工程'
];

echo "\n原始公告数据:\n";
print_r($announcementData);

$actualField2 = \common\base\models\BaseSpecialNeedConfig::convertFieldName('recruitAmount', $announcementData, 'announcement');
echo "recruitAmount字段在公告数据中转换为: {$actualField2}\n";

// 测试3: 应用配置功能
echo "\n\n3. 测试应用配置功能:\n";

// 模拟配置数据
$configs = [
    [
        'field_name' => 'recruitAmount',
        'field_value' => '若干',
        'type' => 'job'
    ],
    [
        'field_name' => 'major',
        'field_value' => '计算机科学与技术、软件工程',
        'type' => 'job'
    ]
];

echo "配置数据:\n";
print_r($configs);

$modifiedJobData = \common\base\models\BaseSpecialNeedConfig::applyConfigs($jobData, $configs, 'job');
echo "\n应用配置后的职位数据:\n";
print_r($modifiedJobData);

// 测试4: 字段显示名称获取
echo "\n\n4. 测试字段显示名称获取:\n";

$testFields = ['recruitAmount', 'amount', 'major', 'education', 'unknown_field'];
foreach ($testFields as $field) {
    $displayName = \common\base\models\BaseSpecialNeedConfig::getFieldDisplayName($field, 'job');
    echo "字段 '{$field}' 的显示名称: {$displayName}\n";
}

// 测试5: 统一字段检查
echo "\n\n5. 测试统一字段检查:\n";
foreach ($testFields as $field) {
    $isUnified = \common\base\models\BaseSpecialNeedConfig::isUnifiedField($field) ? '是' : '否';
    echo "字段 '{$field}' 是否为统一字段: {$isUnified}\n";
}

// 测试6: 获取原始字段名
echo "\n\n6. 测试获取原始字段名:\n";
$originalFields = \common\base\models\BaseSpecialNeedConfig::getOriginalFieldNames('recruitAmount');
echo "统一字段 'recruitAmount' 对应的原始字段: " . implode(', ', $originalFields) . "\n";

echo "\n=== 测试完成 ===\n";
