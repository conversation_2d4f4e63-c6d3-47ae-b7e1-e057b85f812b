<?php
/**
 * 测试特殊需求字段映射功能
 * 用于验证字段统一管理和转换功能是否正常工作
 */

require_once __DIR__ . '/common/base/models/BaseSpecialNeedConfig.php';

echo "=== 特殊需求字段映射功能测试 ===\n\n";

// 测试1: 获取统一后的字段选项
echo "1. 测试获取统一后的字段选项:\n";
$jobFields = \common\base\models\BaseSpecialNeedConfig::getFieldOptions('job');
echo "职位字段选项:\n";
foreach ($jobFields as $key => $label) {
    echo "  {$key} => {$label}\n";
}

$announcementFields = \common\base\models\BaseSpecialNeedConfig::getFieldOptions('announcement');
echo "\n公告字段选项:\n";
foreach ($announcementFields as $key => $label) {
    echo "  {$key} => {$label}\n";
}

$companyFields = \common\base\models\BaseSpecialNeedConfig::getFieldOptions('company');
echo "\n单位字段选项:\n";
foreach ($companyFields as $key => $label) {
    echo "  {$key} => {$label}\n";
}

// 测试2: 字段转换功能
echo "\n\n2. 测试字段转换功能:\n";

// 模拟职位数据（使用amount、major字段）
$jobData = [
    'id' => 123,
    'title' => '软件工程师',
    'amount' => 5,              // 职位使用amount字段
    'education' => '本科',
    'major' => '120,121',       // 专业ID
    'applyType' => '1,2'        // 报名方式ID
];

echo "原始职位数据:\n";
print_r($jobData);

// 测试各种字段转换
$recruitAmountField = \common\base\models\BaseSpecialNeedConfig::convertFieldName('recruitAmount', $jobData, 'job');
echo "recruitAmount字段在职位数据中转换为: {$recruitAmountField}\n";

$majorField = \common\base\models\BaseSpecialNeedConfig::convertFieldName('major', $jobData, 'job');
echo "major字段在职位数据中转换为: {$majorField}\n";

$applyField = \common\base\models\BaseSpecialNeedConfig::convertFieldName('applyType', $jobData, 'job');
echo "applyType字段在职位数据中转换为: {$applyField}\n";

// 模拟公告数据（使用recruitAmount、majorTxt字段）
$announcementData = [
    'id' => 456,
    'title' => '2024年招聘公告',
    'recruitAmount' => 10,           // 公告使用recruitAmount字段
    'education' => '硕士',
    'majorTxt' => '软件工程,计算机科学',  // 专业文本
    'applyTypeText' => '电子邮件,现场报名' // 报名方式文本
];

echo "\n原始公告数据:\n";
print_r($announcementData);

$recruitAmountField2 = \common\base\models\BaseSpecialNeedConfig::convertFieldName('recruitAmount', $announcementData, 'announcement');
echo "recruitAmount字段在公告数据中转换为: {$recruitAmountField2}\n";

$majorField2 = \common\base\models\BaseSpecialNeedConfig::convertFieldName('major', $announcementData, 'announcement');
echo "major字段在公告数据中转换为: {$majorField2}\n";

$applyField2 = \common\base\models\BaseSpecialNeedConfig::convertFieldName('applyType', $announcementData, 'announcement');
echo "applyType字段在公告数据中转换为: {$applyField2}\n";

// 测试3: 应用配置功能
echo "\n\n3. 测试应用配置功能:\n";

// 模拟配置数据（使用统一字段名）
$configs = [
    [
        'field_name' => 'recruitAmount',
        'field_value' => '若干',
        'type' => 'job'
    ],
    [
        'field_name' => 'major',
        'field_value' => '计算机科学与技术、软件工程',
        'type' => 'job'
    ],
    [
        'field_name' => 'applyType',
        'field_value' => '电子邮件,网上报名',
        'type' => 'job'
    ]
];

echo "配置数据:\n";
print_r($configs);

$modifiedJobData = \common\base\models\BaseSpecialNeedConfig::applyConfigs($jobData, $configs, 'job');
echo "\n应用配置后的职位数据:\n";
print_r($modifiedJobData);

// 测试4: 字段显示名称获取
echo "\n\n4. 测试字段显示名称获取:\n";

$testFields = ['recruitAmount', 'major', 'applyType', 'amount', 'majorTxt', 'applyTypeText', 'education', 'unknown_field'];
foreach ($testFields as $field) {
    $displayName = \common\base\models\BaseSpecialNeedConfig::getFieldDisplayName($field, 'job');
    echo "字段 '{$field}' 的显示名称: {$displayName}\n";
}

// 测试5: 统一字段检查
echo "\n\n5. 测试统一字段检查:\n";
foreach ($testFields as $field) {
    $isUnified = \common\base\models\BaseSpecialNeedConfig::isUnifiedField($field) ? '是' : '否';
    echo "字段 '{$field}' 是否为统一字段: {$isUnified}\n";
}

// 测试6: 获取原始字段名
echo "\n\n6. 测试获取原始字段名:\n";
$unifiedFields = ['recruitAmount', 'major', 'applyType'];
foreach ($unifiedFields as $unifiedField) {
    $originalFields = \common\base\models\BaseSpecialNeedConfig::getOriginalFieldNames($unifiedField);
    echo "统一字段 '{$unifiedField}' 对应的原始字段: " . implode(', ', $originalFields) . "\n";
}

echo "\n=== 历史遗留问题解决方案总结 ===\n";
echo "1. 招聘人数: amount + recruitAmount → recruitAmount (统一)\n";
echo "2. 专业要求: major + majorTxt → major (统一，使用major作为主字段)\n";
echo "3. 报名方式: applyType + applyTypeText → applyType (统一，使用applyType作为主字段)\n";
echo "4. 系统会根据数据结构智能选择实际使用的字段名\n";
echo "5. 前端用户只看到统一的选项，不再有重复和混乱\n";

echo "\n=== 测试完成 ===\n";
