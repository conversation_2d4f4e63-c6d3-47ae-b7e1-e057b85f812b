<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "special_need_apply_limit".
 *
 * @property int $id
 * @property string $name 限制名称
 * @property int $company_id 单位ID
 * @property string $limit_type 限制类型：count=次数限制,condition=条件限制
 * @property int $time_limit 时间限制（天）
 * @property int $count_limit 次数限制
 * @property string $condition_field 条件字段（如is_abroad）
 * @property string $condition_value 条件值
 * @property string $error_message 错误提示信息
 * @property int $status 状态：0=禁用，1=启用
 * @property string $remark 备注说明
 * @property int $created_by 创建人ID
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 */
class SpecialNeedApplyLimit extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'special_need_apply_limit';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['company_id', 'time_limit', 'count_limit', 'status', 'created_by'], 'integer'],
            [['remark'], 'string'],
            [['add_time', 'update_time'], 'safe'],
            [['name', 'condition_value'], 'string', 'max' => 100],
            [['limit_type'], 'string', 'max' => 20],
            [['condition_field'], 'string', 'max' => 50],
            [['error_message'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'company_id' => 'Company ID',
            'limit_type' => 'Limit Type',
            'time_limit' => 'Time Limit',
            'count_limit' => 'Count Limit',
            'condition_field' => 'Condition Field',
            'condition_value' => 'Condition Value',
            'error_message' => 'Error Message',
            'status' => 'Status',
            'remark' => 'Remark',
            'created_by' => 'Created By',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
        ];
    }
}
