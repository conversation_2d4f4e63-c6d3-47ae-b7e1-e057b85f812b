<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_click_update_config".
 *
 * @property int $id
 * @property string $class_name 脚本类名称
 * @property string $update_time 脚本更新时间
 * @property int $start_id 执行开始节点
 * @property int $end_id 执行结束节点
 * @property string $start_time 执行开始时间节点
 * @property string $end_time 执行结束时间节点
 * @property int $limit_max 最大limit数量
 * @property int $real_execute_status 实时脚本执行状态,0:未完成,1:已完成
 * @property int $history_execute_status 历史脚本执行状态,0:未完成,1:已完成
 * @property int $history_log_complete_status 历史数据完成状态,0:未完成,1:已完成
 * @property string $real_fail_msg 实时脚本错误信息
 * @property string $history_fail_msg 历史脚本错误信息
 */
class JobClickUpdateConfig extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_click_update_config';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['update_time', 'start_time', 'end_time'], 'safe'],
            [['start_id', 'end_id', 'limit_max', 'real_execute_status', 'history_execute_status', 'history_log_complete_status'], 'integer'],
            [['real_fail_msg', 'history_fail_msg'], 'string'],
            [['class_name'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'class_name' => 'Class Name',
            'update_time' => 'Update Time',
            'start_id' => 'Start ID',
            'end_id' => 'End ID',
            'start_time' => 'Start Time',
            'end_time' => 'End Time',
            'limit_max' => 'Limit Max',
            'real_execute_status' => 'Real Execute Status',
            'history_execute_status' => 'History Execute Status',
            'history_log_complete_status' => 'History Log Complete Status',
            'real_fail_msg' => 'Real Fail Msg',
            'history_fail_msg' => 'History Fail Msg',
        ];
    }
}
