<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_apply_education_total_daily".
 *
 * @property int $id ID
 * @property string $add_date 时间格式
 * @property int $job_id 职位ID
 * @property int $rests 其他
 * @property int $college 大专
 * @property int $poaceae 本科
 * @property int $doctor 博士
 * @property int $master 硕士
 */
class JobApplyEducationTotalDaily extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_apply_education_total_daily';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_date'], 'required'],
            [['add_date'], 'safe'],
            [['job_id', 'rests', 'college', 'poaceae', 'doctor', 'master'], 'integer'],
            [['job_id', 'add_date'], 'unique', 'targetAttribute' => ['job_id', 'add_date']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_date' => 'Add Date',
            'job_id' => 'Job ID',
            'rests' => 'Rests',
            'college' => 'College',
            'poaceae' => 'Poaceae',
            'doctor' => 'Doctor',
            'master' => 'Master',
        ];
    }
}
