<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "special_need_config".
 *
 * @property int $id
 * @property string $name 配置名称
 * @property string $type 类型：company,announcement,job,apply_limit
 * @property int $target_id 目标ID（单位ID/公告ID/职位ID）
 * @property string $field_name 字段名称
 * @property string $field_value 字段值
 * @property string $platform 适用平台：ALL,PC,H5,MINI
 * @property int $status 状态：0=禁用，1=启用
 * @property string $start_time 生效开始时间
 * @property string $end_time 生效结束时间
 * @property string $remark 备注说明
 * @property int $created_by 创建人ID
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 */
class SpecialNeedConfig extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'special_need_config';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['target_id', 'status', 'created_by'], 'integer'],
            [['field_value', 'remark'], 'string'],
            [['start_time', 'end_time', 'add_time', 'update_time'], 'safe'],
            [['name'], 'string', 'max' => 100],
            [['type', 'platform'], 'string', 'max' => 20],
            [['field_name'], 'string', 'max' => 50],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'type' => 'Type',
            'target_id' => 'Target ID',
            'field_name' => 'Field Name',
            'field_value' => 'Field Value',
            'platform' => 'Platform',
            'status' => 'Status',
            'start_time' => 'Start Time',
            'end_time' => 'End Time',
            'remark' => 'Remark',
            'created_by' => 'Created By',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
        ];
    }
}
