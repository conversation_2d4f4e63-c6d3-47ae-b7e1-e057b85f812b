<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_tag".
 *
 * @property int $id
 * @property string $add_time 新增时间
 * @property string $update_time 更新时间
 * @property string $tag 特色标签内容
 * @property int $admin_id 添加人id
 */
class ResumeTag extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_tag';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['admin_id'], 'integer'],
            [['tag'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'tag' => 'Tag',
            'admin_id' => 'Admin ID',
        ];
    }
}
