<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_intention".
 *
 * @property int $id id;主键id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态
 * @property int $resume_id 简历id
 * @property int $member_id 会员id
 * @property int $nature_type 工作性质(1全职，2兼职，3实习)
 * @property int $job_category_id 意向职位id
 * @property string $area_id 意向地区id
 * @property string $min_wage 最低期望薪酬
 * @property string $max_wage 最高期望薪酬
 * @property int $wage_type 期望薪酬类型
 */
class ResumeIntention extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_intention';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'resume_id', 'member_id', 'nature_type', 'job_category_id', 'wage_type'], 'integer'],
            [['area_id'], 'string', 'max' => 256],
            [['min_wage', 'max_wage'], 'string', 'max' => 60],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
            'nature_type' => 'Nature Type',
            'job_category_id' => 'Job Category ID',
            'area_id' => 'Area ID',
            'min_wage' => 'Min Wage',
            'max_wage' => 'Max Wage',
            'wage_type' => 'Wage Type',
        ];
    }
}
