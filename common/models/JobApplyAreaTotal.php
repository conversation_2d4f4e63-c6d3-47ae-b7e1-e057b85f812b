<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_apply_area_total".
 *
 * @property int $id ID
 * @property string $update_time 更新时间
 * @property int $job_id 职位ID
 * @property int $city_total 城市匹配投递量
 * @property int $province_total 省份匹配投递量
 */
class JobApplyAreaTotal extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_apply_area_total';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['update_time'], 'safe'],
            [['job_id', 'city_total', 'province_total'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'update_time' => 'Update Time',
            'job_id' => 'Job ID',
            'city_total' => 'City Total',
            'province_total' => 'Province Total',
        ];
    }
}
