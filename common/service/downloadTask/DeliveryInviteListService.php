<?php

namespace common\service\downloadTask;

use admin\models\DeliveryInvite;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseAdminJobInviteConfig;
use common\base\models\BaseDictionary;
use common\base\models\BaseJobApply;
use common\base\models\BaseMajor;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\helpers\UUIDHelper;

class DeliveryInviteListService
{
    /**
     * 导出数据准备
     * @param $params
     * @return array
     * @throws \yii\base\Exception
     */
    public function run($params)
    {
        if ($params['portType'] == 1) {
            $rs       = DeliveryInvite::getListAdminInvite($params);
            $fileName = '运营端_' . BaseAdminDownloadTask::TYPE_DELIVERY_INVITE_LIST_NAME . '_' . date('YmdHis');
        } else {
            $rs       = DeliveryInvite::getListCompanyInvite($params);
            $fileName = '单位端_' . BaseAdminDownloadTask::TYPE_DELIVERY_INVITE_LIST_NAME . '_' . date('YmdHis');
        }

        return [
            'data'     => $rs['list'],
            'headers'  => $rs['headers'],
            'fileName' => $fileName,
        ];
    }
}