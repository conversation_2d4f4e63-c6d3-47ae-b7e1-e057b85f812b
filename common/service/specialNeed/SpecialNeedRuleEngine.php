<?php

namespace common\service\specialNeed;

use common\base\models\BaseSpecialNeedRule;
use common\base\models\BaseSpecialNeedRuleLog;
use common\base\models\BaseSystemConfig;
use Yii;
use yii\base\Component;
use yii\caching\TagDependency;

/**
 * 特殊需求规则引擎
 * 负责加载、匹配和执行特殊需求规则
 */
class SpecialNeedRuleEngine extends Component
{
    /**
     * 规则缓存键前缀
     */
    const CACHE_KEY_PREFIX = 'special_need_rules_';
    
    /**
     * 缓存标签
     */
    const CACHE_TAG = 'special_need_rules';
    
    /**
     * 是否启用日志记录
     * @var bool
     */
    public $logEnabled = true;
    
    /**
     * 当前平台
     * @var string
     */
    protected $platform;
    
    /**
     * 规则缓存
     * @var array
     */
    protected $rulesCache = [];
    
    public function init()
    {
        parent::init();
        
        // 获取当前平台
        $this->platform = defined('PLATFORM') ? PLATFORM : 'PC';
        
        // 检查日志开关
        $this->logEnabled = BaseSystemConfig::getValueByKey('special_need_log_enabled', 1) == 1;
    }
    
    /**
     * 执行特殊需求规则
     * 
     * @param string $serviceType 服务类型
     * @param string $methodName 方法名称
     * @param array $data 数据
     * @param array $context 上下文信息
     * @return array 处理后的数据
     */
    public function execute($serviceType, $methodName, $data, $context = [])
    {
        $startTime = microtime(true);
        
        try {
            // 检查功能是否启用
            if (!$this->isEnabled()) {
                return $data;
            }
            
            // 获取适用的规则
            $rules = $this->getRules($serviceType, $methodName);
            
            if (empty($rules)) {
                return $data;
            }
            
            // 按优先级排序执行规则
            usort($rules, function($a, $b) {
                return $b['priority'] - $a['priority'];
            });
            
            $originalData = $data;
            $executedRules = [];
            
            foreach ($rules as $rule) {
                if ($this->shouldExecuteRule($rule, $data, $context)) {
                    $data = $this->executeRule($rule, $data, $context);
                    $executedRules[] = $rule['id'];
                }
            }
            
            // 记录执行日志
            if ($this->logEnabled && !empty($executedRules)) {
                $this->logExecution($executedRules, $originalData, $data, $context, microtime(true) - $startTime);
            }
            
            return $data;
            
        } catch (\Exception $e) {
            // 记录错误日志
            if ($this->logEnabled) {
                $this->logError($serviceType, $methodName, $data, $context, $e, microtime(true) - $startTime);
            }
            
            // 发生错误时返回原始数据，确保系统稳定性
            Yii::error("特殊需求规则执行失败: " . $e->getMessage(), __CLASS__);
            return $data;
        }
    }
    
    /**
     * 获取指定服务和方法的规则
     * 
     * @param string $serviceType
     * @param string $methodName
     * @return array
     */
    protected function getRules($serviceType, $methodName)
    {
        $cacheKey = self::CACHE_KEY_PREFIX . $serviceType . '_' . $methodName;
        
        // 先从内存缓存获取
        if (isset($this->rulesCache[$cacheKey])) {
            return $this->rulesCache[$cacheKey];
        }
        
        // 从Redis缓存获取
        $rules = Yii::$app->cache->get($cacheKey);
        
        if ($rules === false) {
            // 从数据库加载
            $rules = BaseSpecialNeedRule::find()
                ->where([
                    'service_type' => $serviceType,
                    'method_name' => $methodName,
                    'status' => BaseSpecialNeedRule::STATUS_ENABLED
                ])
                ->andWhere(['or',
                    ['start_time' => null],
                    ['<=', 'start_time', date('Y-m-d H:i:s')]
                ])
                ->andWhere(['or',
                    ['end_time' => null],
                    ['>=', 'end_time', date('Y-m-d H:i:s')]
                ])
                ->asArray()
                ->all();
            
            // 过滤适用平台的规则
            $rules = array_filter($rules, function($rule) {
                $platforms = explode(',', $rule['platforms']);
                return in_array($this->platform, $platforms);
            });
            
            // 缓存规则
            $cacheTtl = BaseSystemConfig::getValueByKey('special_need_cache_ttl', 3600);
            Yii::$app->cache->set($cacheKey, $rules, $cacheTtl, new TagDependency(['tags' => [self::CACHE_TAG]]));
        }
        
        // 存储到内存缓存
        $this->rulesCache[$cacheKey] = $rules;
        
        return $rules;
    }
    
    /**
     * 判断是否应该执行规则
     * 
     * @param array $rule
     * @param array $data
     * @param array $context
     * @return bool
     */
    protected function shouldExecuteRule($rule, $data, $context)
    {
        if (empty($rule['trigger_conditions'])) {
            return true;
        }
        
        $conditions = json_decode($rule['trigger_conditions'], true);
        if (!$conditions) {
            return true;
        }
        
        return $this->evaluateConditions($conditions, $data, $context);
    }
    
    /**
     * 评估条件
     * 
     * @param array $conditions
     * @param array $data
     * @param array $context
     * @return bool
     */
    protected function evaluateConditions($conditions, $data, $context)
    {
        $type = $conditions['type'] ?? 'AND';
        $conditionList = $conditions['conditions'] ?? [];
        
        if (empty($conditionList)) {
            return true;
        }
        
        $results = [];
        foreach ($conditionList as $condition) {
            $results[] = $this->evaluateCondition($condition, $data, $context);
        }
        
        if ($type === 'OR') {
            return in_array(true, $results);
        } else {
            return !in_array(false, $results);
        }
    }
    
    /**
     * 评估单个条件
     * 
     * @param array $condition
     * @param array $data
     * @param array $context
     * @return bool
     */
    protected function evaluateCondition($condition, $data, $context)
    {
        $field = $condition['field'];
        $operator = $condition['operator'];
        $expectedValue = $condition['value'];
        $dataType = $condition['data_type'] ?? 'string';
        
        // 获取实际值
        $actualValue = $this->getFieldValue($field, $data, $context);
        
        // 类型转换
        $actualValue = $this->convertDataType($actualValue, $dataType);
        $expectedValue = $this->convertDataType($expectedValue, $dataType);
        
        // 执行比较
        return $this->compareValues($actualValue, $operator, $expectedValue);
    }
    
    /**
     * 获取字段值
     * 
     * @param string $field
     * @param array $data
     * @param array $context
     * @return mixed
     */
    protected function getFieldValue($field, $data, $context)
    {
        // 优先从context获取
        if (isset($context[$field])) {
            return $context[$field];
        }
        
        // 从data获取
        if (isset($data[$field])) {
            return $data[$field];
        }
        
        // 支持点号分隔的嵌套字段
        if (strpos($field, '.') !== false) {
            $keys = explode('.', $field);
            $value = $data;
            foreach ($keys as $key) {
                if (is_array($value) && isset($value[$key])) {
                    $value = $value[$key];
                } else {
                    return null;
                }
            }
            return $value;
        }
        
        return null;
    }
    
    /**
     * 数据类型转换
     * 
     * @param mixed $value
     * @param string $dataType
     * @return mixed
     */
    protected function convertDataType($value, $dataType)
    {
        switch ($dataType) {
            case 'number':
                return is_numeric($value) ? (float)$value : 0;
            case 'array':
                return is_array($value) ? $value : [$value];
            case 'string':
            default:
                return (string)$value;
        }
    }
    
    /**
     * 比较值
     * 
     * @param mixed $actual
     * @param string $operator
     * @param mixed $expected
     * @return bool
     */
    protected function compareValues($actual, $operator, $expected)
    {
        switch ($operator) {
            case '=':
            case '==':
                return $actual == $expected;
            case '!=':
                return $actual != $expected;
            case '>':
                return $actual > $expected;
            case '<':
                return $actual < $expected;
            case '>=':
                return $actual >= $expected;
            case '<=':
                return $actual <= $expected;
            case 'in':
                return is_array($expected) && in_array($actual, $expected);
            case 'not_in':
                return is_array($expected) && !in_array($actual, $expected);
            case 'like':
                return strpos((string)$actual, (string)$expected) !== false;
            case 'not_like':
                return strpos((string)$actual, (string)$expected) === false;
            default:
                return false;
        }
    }
    
    /**
     * 执行规则
     * 
     * @param array $rule
     * @param array $data
     * @param array $context
     * @return array
     */
    protected function executeRule($rule, $data, $context)
    {
        if (empty($rule['actions'])) {
            return $data;
        }
        
        $actions = json_decode($rule['actions'], true);
        if (!$actions) {
            return $data;
        }
        
        $actionList = $actions['actions'] ?? [];
        
        foreach ($actionList as $action) {
            $data = $this->executeAction($action, $data, $context);
        }
        
        return $data;
    }
    
    /**
     * 执行动作
     * 
     * @param array $action
     * @param array $data
     * @param array $context
     * @return array
     */
    protected function executeAction($action, $data, $context)
    {
        $actionType = $action['action_type'];
        
        switch ($actionType) {
            case 'replace_field':
                return $this->executeReplaceField($action, $data, $context);
            case 'add_field':
                return $this->executeAddField($action, $data, $context);
            case 'validation':
                return $this->executeValidation($action, $data, $context);
            default:
                return $data;
        }
    }
    
    /**
     * 执行字段替换
     * 
     * @param array $action
     * @param array $data
     * @param array $context
     * @return array
     */
    protected function executeReplaceField($action, $data, $context)
    {
        $targetField = $action['target_field'];
        $newValue = $action['new_value'];
        $platformSpecific = $action['platform_specific'] ?? [];
        
        // 检查是否有平台特定值
        if (isset($platformSpecific[$this->platform])) {
            $newValue = $platformSpecific[$this->platform];
        }
        
        // 支持嵌套字段
        if (strpos($targetField, '.') !== false) {
            $this->setNestedValue($data, $targetField, $newValue);
        } else {
            $data[$targetField] = $newValue;
        }
        
        return $data;
    }
    
    /**
     * 执行字段添加
     * 
     * @param array $action
     * @param array $data
     * @param array $context
     * @return array
     */
    protected function executeAddField($action, $data, $context)
    {
        $targetField = $action['target_field'];
        $appendValue = $action['append_value'];
        $separator = $action['separator'] ?? '';
        
        $currentValue = $data[$targetField] ?? '';
        $data[$targetField] = $currentValue . $separator . $appendValue;
        
        return $data;
    }
    
    /**
     * 执行验证
     * 
     * @param array $action
     * @param array $data
     * @param array $context
     * @return array
     * @throws \Exception
     */
    protected function executeValidation($action, $data, $context)
    {
        $validationType = $action['validation_type'];
        $validationConfig = $action['validation_config'];
        
        switch ($validationType) {
            case 'apply_limit':
                $this->validateApplyLimit($validationConfig, $data, $context);
                break;
        }
        
        return $data;
    }
    
    /**
     * 验证投递限制
     * 
     * @param array $config
     * @param array $data
     * @param array $context
     * @throws \Exception
     */
    protected function validateApplyLimit($config, $data, $context)
    {
        $timeLimit = $config['time_limit'];
        $timeUnit = $config['time_unit'];
        $maxCount = $config['max_count'];
        $errorMessage = $config['error_message'];
        
        $companyId = $context['companyId'] ?? 0;
        $resumeId = $context['resumeId'] ?? 0;
        
        if (!$companyId || !$resumeId) {
            return;
        }
        
        // 计算时间范围
        $timeRange = "-{$timeLimit} {$timeUnit}";
        $startTime = date('Y-m-d H:i:s', strtotime($timeRange));
        
        // 查询投递记录
        $count = \common\base\models\BaseJobApplyRecord::find()
            ->where([
                'resume_id' => $resumeId,
                'company_id' => $companyId,
            ])
            ->andWhere(['>=', 'add_time', $startTime])
            ->count();
        
        if ($count >= $maxCount) {
            throw new \common\exceptions\MessageException($errorMessage);
        }
    }
    
    /**
     * 设置嵌套字段值
     * 
     * @param array &$data
     * @param string $field
     * @param mixed $value
     */
    protected function setNestedValue(&$data, $field, $value)
    {
        $keys = explode('.', $field);
        $current = &$data;
        
        foreach ($keys as $key) {
            if (!isset($current[$key])) {
                $current[$key] = [];
            }
            $current = &$current[$key];
        }
        
        $current = $value;
    }
    
    /**
     * 记录执行日志
     * 
     * @param array $ruleIds
     * @param array $originalData
     * @param array $resultData
     * @param array $context
     * @param float $executionTime
     */
    protected function logExecution($ruleIds, $originalData, $resultData, $context, $executionTime)
    {
        foreach ($ruleIds as $ruleId) {
            try {
                $log = new BaseSpecialNeedRuleLog();
                $log->rule_id = $ruleId;
                $log->trigger_data = json_encode($originalData);
                $log->execution_result = json_encode($resultData);
                $log->execution_time = $executionTime;
                $log->status = BaseSpecialNeedRuleLog::STATUS_SUCCESS;
                $log->platform = $this->platform;
                $log->save();
            } catch (\Exception $e) {
                // 日志记录失败不影响主流程
                Yii::error("记录特殊需求执行日志失败: " . $e->getMessage(), __CLASS__);
            }
        }
    }
    
    /**
     * 记录错误日志
     * 
     * @param string $serviceType
     * @param string $methodName
     * @param array $data
     * @param array $context
     * @param \Exception $exception
     * @param float $executionTime
     */
    protected function logError($serviceType, $methodName, $data, $context, $exception, $executionTime)
    {
        try {
            $log = new BaseSpecialNeedRuleLog();
            $log->rule_id = 0;
            $log->trigger_data = json_encode(['service_type' => $serviceType, 'method_name' => $methodName, 'data' => $data, 'context' => $context]);
            $log->execution_result = json_encode(['error' => $exception->getMessage()]);
            $log->execution_time = $executionTime;
            $log->status = BaseSpecialNeedRuleLog::STATUS_FAILED;
            $log->error_message = $exception->getMessage();
            $log->platform = $this->platform;
            $log->save();
        } catch (\Exception $e) {
            // 日志记录失败不影响主流程
            Yii::error("记录特殊需求错误日志失败: " . $e->getMessage(), __CLASS__);
        }
    }
    
    /**
     * 检查功能是否启用
     * 
     * @return bool
     */
    protected function isEnabled()
    {
        return BaseSystemConfig::getValueByKey('special_need_enabled', 1) == 1;
    }
    
    /**
     * 清除规则缓存
     */
    public static function clearCache()
    {
        TagDependency::invalidate(Yii::$app->cache, [self::CACHE_TAG]);
    }
}
