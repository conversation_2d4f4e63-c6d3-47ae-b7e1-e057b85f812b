<?php

namespace common\base\models;

use common\helpers\FormatConverter;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\libs\JobBatchImport;
use common\models\JobTemp;
use common\service\announcement\BatchJobService;
use frontendPc\models\Member;
use yii\base\Exception;
use Yii;

class BaseJobTemp extends JobTemp
{
    const IS_TEMP_YES = 1;
    const IS_TEMP_NO  = 2;

    public function attributeLabels()
    {
        return [
            'requirement' => '任职要求',
            'name' => '职位名称',
            'duty' => '岗位职责',
            'apply_address' => '投递地址',
            'remark' => '其他说明',
            'amount' => '招聘人数',
            'department' => '用人部门',
            'code' => '职位代码',
        ];
    }

    /**
     * 获取单条职位信息
     * @param $where
     * @param $select
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function selectInfo($where, $select)
    {
        return self::find()
            ->where($where)
            ->select($select)
            ->asArray()
            ->one();
    }

    /**
     * 获取公告临时职位列表
     * @param $params
     * @return array
     */
    public static function getJobTempList($params)
    {
        $select = [
            'id',
            'code',
            'name',
            'major_id',
            'amount',
            'wage_type',
            'min_wage',
            'max_wage',
            'department',
            'province_id',
            'city_id',
            'district_id',
            'address',
            'is_temp',
        ];

        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];

        $list = self::find()
            ->where([
                'in',
                'id',
                $params['jobTempId'],
            ])
            ->select($select)
            ->limit($pageSize)
            ->asArray()
            ->all();

        //获取地区表缓存
        $cache     = Yii::$app->cache;
        $areaCache = $cache->get(Cache::PC_ALL_AREA_TABLE_KEY);
        if (!$areaCache) {
            $areaCache = BaseArea::setAreaCache();
        }

        foreach ($list as &$item) {
            $item['majorTxt'] = BaseMajor::getMajorName($item['major_id']);
            $item['areaName'] = $areaCache[$item['province_id']]['name'] . $areaCache[$item['city_id']]['name'];
        }

        return [
            'list' => $list,
        ];
    }

    /**
     * 获取临时职位编辑数据
     * @throws Exception
     * @throws \Exception
     */
    public static function getTemporaryJobEdit($params)
    {
        $jobWhere   = ['id' => $params['id']];
        $jobSelect  = [
            'id',
            'name',
            'period_date',
            'code',
            'job_category_id',
            'education_type',
            'company_id',
            'major_id',
            'nature_type',
            'is_negotiable',
            'wage_type',
            'min_wage',
            'max_wage',
            'experience_type',
            'age_type',
            'min_age',
            'max_age',
            'title_type',
            'political_type',
            'abroad_type',
            'amount',
            'department',
            'province_id',
            'city_id',
            'district_id',
            'address',
            'welfare_tag',
            'duty',
            'requirement',
            'remark',
            'audit_status',
            'apply_type',
            'apply_address',
            'is_temp',
            'announcement_id',
            'job_id',
            'delivery_limit_type',
            'delivery_type',
            'delivery_way',
            'extra_notify_address',
            'establishment_type',
            'contact_id',
            'contact_synergy_id',
        ];
        $jobDetails = self::selectInfo($jobWhere, $jobSelect);
        if (empty($jobDetails)) {
            return [];
        }
        //投递方式处理
        if (in_array($jobDetails['delivery_way'], BaseJob::DELIVERY_WAY_EMAIL_LINK_LIST)) {
            $jobDetails['delivery_way'] = (string)BaseJob::DELIVERY_WAY_EMAIL_LINK;
        } elseif (empty($jobDetails['delivery_way'])) {
            $jobDetails['delivery_way'] = '';
        }
        if ($jobDetails['experience_type'] == '0') {
            $jobDetails['experience_type'] = '';
        }
        if ($jobDetails['political_type'] == '0') {
            $jobDetails['political_type'] = '';
        }
        if ($jobDetails['abroad_type'] == '0') {
            $jobDetails['abroad_type'] = '';
        }
        if ($jobDetails['nature_type'] == '0') {
            $jobDetails['nature_type'] = '';
        }
        if ($jobDetails['wage_type'] == '0') {
            $jobDetails['wage_type'] = '';
        }
        if ($jobDetails['age_type'] == '0') {
            $jobDetails['age_type'] = '';
        }
        if ($jobDetails['title_type'] == '0') {
            $jobDetails['title_type'] = '';
        }
        if ($jobDetails['min_wage'] == '0') {
            $jobDetails['min_wage'] = '';
        }
        if ($jobDetails['max_wage'] == '0') {
            $jobDetails['max_wage'] = '';
        }
        if ($jobDetails['min_age'] == '0') {
            $jobDetails['min_age'] = '';
        }
        if ($jobDetails['max_age'] == '0') {
            $jobDetails['max_age'] = '';
        }
        if ($jobDetails['district_id'] == '0') {
            $jobDetails['district_id'] = '';
        }
        if (empty($jobDetails['delivery_type'])) {
            $jobDetails['delivery_type'] = '';
        }
        if ($jobDetails['period_date'] == TimeHelper::ZERO_TIME) {
            $jobDetails['period_date'] = '';
        }
        if ($jobDetails['amount'] == '-1') {
            $jobDetails['amount'] = '若干';
        }

        //获取地区表缓存
        $cache     = Yii::$app->cache;
        $areaCache = $cache->get(Cache::PC_ALL_AREA_TABLE_KEY);
        if (!$areaCache) {
            $areaCache = BaseArea::setAreaCache();
        }

        $jobDetails['company_area_name'] = $areaCache[$jobDetails['province_id']]['name'] . $areaCache[$jobDetails['city_id']]['name'];
        $jobDetails['area_name']         = $areaCache[$jobDetails['province_id']]['id'] . ',' . $areaCache[$jobDetails['city_id']]['id'];
        $jobDetails['area_name']         = explode(',', $jobDetails['area_name']);
        // 地区为空返回空数组
        if (!$areaCache[$jobDetails['city_id']]['id']) {
            $jobDetails['area_name'] = [];
        }

        //薪资code回显
        if ($jobDetails['is_negotiable'] <> 1) {
            $jobDetails['wage_id'] = (string)BaseJob::getWageId($jobDetails['min_wage'], $jobDetails['max_wage']);
        }
        $jobDetails['wage'] = BaseJob::formatWage($jobDetails['min_wage'], $jobDetails['max_wage'],
            $jobDetails['wage_type']);

        //查询福利标签
        $welfareLabelWhere  = ['id' => explode(',', $jobDetails['welfare_tag'])];
        $welfareLabelSelect = [
            'id',
            'name',
        ];

        //学科专业
        if ($jobDetails['major_id']) {
            $majorIds                 = explode(',', $jobDetails['major_id']);
            $jobDetails['majorTitle'] = BaseMajor::getAllMajorName($majorIds);
            if (strlen($jobDetails['major_id']) < 1) {
                $jobDetails['majorTitle'] = "专业不限";
            }
        }

        $welfareLabelList          = BaseWelfareLabel::findList($welfareLabelWhere, $welfareLabelSelect);
        $welfareTag                = [];
        $jobDetails['welfareTage'] = [];
        foreach ($welfareLabelList as $k => $welfareLabel) {
            $welfareTag[$k]['k']       = $welfareLabel['id'];
            $welfareTag[$k]['v']       = $welfareLabel['name'];
            $jobDetails['welfareTage'] = $welfareTag;
        }

        //职位模版id
        if ($params['job_template_id']) {
            $jobDetails['job_template_id'] = $params['job_template_id'];
        }

        //职位联系人
        $contact                      = BaseCompanyMemberInfo::find()
            ->where(['id' => $jobDetails['contact_id']])
            ->asArray()
            ->one();
        $jobDetails['job_contact']    = $contact;
        $jobDetails['job_contact_id'] = $contact['id'];
        //职位协同账号
        $contact_synergy                       = BaseCompanyMemberInfo::find()
            ->where(['id' => explode(',', $jobDetails['contact_synergy_id'])])
            ->asArray()
            ->all();
        $jobDetails['job_contact_synergy']     = $contact_synergy;
        $jobDetails['job_contact_synergy_num'] = count($contact_synergy);
        $jobDetails['job_contact_synergy_ids'] = $jobDetails['job_contact_synergy_num'] > 0 ? array_column($contact_synergy,
            'id') : [];

        return $jobDetails;
    }

    /**
     * 职位批量导入数据
     * @throws Exception
     */
    public static function jobTemporaryBatchImport($keywords, $type): array
    {
        $filePath = Yii::getAlias('@frontendPc') . '/web/' . $keywords['filePath'];

        // 判断一下文件是否存在
        if (!file_exists($filePath)) {
            throw new Exception('文件不存在');
        }

        // 读取数据
        $model = new JobBatchImport();
        $model->identify($filePath, $type);
        $data = $model->clearData;
        // 插入数据
        $adminId     = Yii::$app->user->id;
        $jobTempData = [];
        foreach ($data as $item) {
            $jobData                    = array_column($item, 'value', 'key');
            $jobData                    = FormatConverter::convertHump($jobData);
            $jobData['announcement_id'] = $keywords['announcementId'];
            $jobData['company_id']      = $keywords['companyId'];

            $batchJobService = new BatchJobService();
            $batchJobService->setOperator($adminId, BaseCompany::setDeliveryTypeCate($type))
                ->setData($jobData)
                ->run();

            $jobTempData[] = $batchJobService->jobTempData;
        }

        //删除文件
        unlink($filePath);

        return $jobTempData;
    }

}