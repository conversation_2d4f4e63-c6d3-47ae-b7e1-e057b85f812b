<?php

namespace common\base\models;

use common\models\SpecialNeedConfig;

/**
 * 特殊需求配置模型
 *
 * @property int    $id
 * @property string $name        配置名称
 * @property string $type        类型：company,announcement,job
 * @property int    $target_id   目标ID（单位ID/公告ID/职位ID）
 * @property string $field_name  字段名称
 * @property string $field_value 字段值
 * @property string $platform    适用平台：ALL,PC,H5,MINI
 * @property int    $status      状态：0=禁用，1=启用
 * @property string $start_time  生效开始时间
 * @property string $end_time    生效结束时间
 * @property string $remark      备注说明
 * @property int    $created_by  创建人ID
 * @property string $add_time    创建时间
 * @property string $update_time 更新时间
 */
class BaseSpecialNeedConfig extends SpecialNeedConfig
{
    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED  = 1;

    // 类型常量
    const TYPE_COMPANY      = 'company';
    const TYPE_ANNOUNCEMENT = 'announcement';
    const TYPE_JOB          = 'job';

    // 平台常量
    const PLATFORM_ALL  = 'ALL';
    const PLATFORM_PC   = 'PC';
    const PLATFORM_H5   = 'H5';
    const PLATFORM_MINI = 'MINI';

    /**
     * 字段映射配置 - 用于统一管理相同含义但不同key的字段
     * 格式：'统一字段名' => ['实际字段名1', '实际字段名2', ...]
     */
    const FIELD_MAPPING = [
        'recruitAmount' => ['amount', 'recruitAmount'], // 招聘人数的统一映射
        'major'         => ['major'],                   // 专业要求
        'majorTxt'      => ['majorTxt'],               // 专业要求文本
        'education'     => ['education'],              // 学历要求
        'applyType'     => ['applyType'],              // 报名方式
        'applyTypeText' => ['applyTypeText'],          // 报名方式文本
        'jobCategory'   => ['jobCategory'],            // 职位类别
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'special_need_config';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'name',
                    'type',
                    'target_id',
                    'field_name',
                ],
                'required',
            ],
            [
                [
                    'target_id',
                    'status',
                    'created_by',
                ],
                'integer',
            ],
            [
                [
                    'field_value',
                    'remark',
                ],
                'string',
            ],
            [
                [
                    'start_time',
                    'end_time',
                    'add_time',
                    'update_time',
                ],
                'safe',
            ],
            [
                ['name'],
                'string',
                'max' => 100,
            ],
            [
                [
                    'type',
                    'platform',
                ],
                'string',
                'max' => 20,
            ],
            [
                ['field_name'],
                'string',
                'max' => 50,
            ],
            [
                ['type'],
                'in',
                'range' => [
                    self::TYPE_COMPANY,
                    self::TYPE_ANNOUNCEMENT,
                    self::TYPE_JOB,
                ],
            ],
            [
                ['platform'],
                'in',
                'range' => [
                    self::PLATFORM_ALL,
                    self::PLATFORM_PC,
                    self::PLATFORM_H5,
                    self::PLATFORM_MINI,
                ],
            ],
            [
                ['status'],
                'in',
                'range' => [
                    self::STATUS_DISABLED,
                    self::STATUS_ENABLED,
                ],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'          => 'ID',
            'name'        => '配置名称',
            'type'        => '配置类型',
            'target_id'   => '目标ID',
            'field_name'  => '字段名称',
            'field_value' => '字段值',
            'platform'    => '适用平台',
            'status'      => '状态',
            'start_time'  => '生效开始时间',
            'end_time'    => '生效结束时间',
            'remark'      => '备注说明',
            'created_by'  => '创建人',
            'add_time'    => '创建时间',
            'update_time' => '更新时间',
        ];
    }

    /**
     * 获取类型选项
     * @return array
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_JOB          => '职位信息',
            self::TYPE_ANNOUNCEMENT => '公告信息',
            self::TYPE_COMPANY      => '单位信息',
        ];
    }

    /**
     * 获取平台选项
     * @return array
     */
    public static function getPlatformOptions()
    {
        return [
            self::PLATFORM_ALL  => '全平台',
            self::PLATFORM_PC   => 'PC端',
            self::PLATFORM_H5   => 'H5端',
            self::PLATFORM_MINI => '小程序',
        ];
    }

    /**
     * 获取字段选项
     * @param string $type
     * @return array
     */
    public static function getFieldOptions($type = '')
    {
        $options = [
            self::TYPE_JOB          => [
                'education'     => '学历要求',
                'major'         => '专业要求',
                'majorTxt'      => '专业要求文本',
                'amount'        => '招聘人数',
                'applyType'     => '报名方式',
                'applyTypeText' => '报名方式文本',
                'jobCategory'   => '职位类别',
            ],
            self::TYPE_ANNOUNCEMENT => [
                'education'     => '学历要求',
                'major'         => '专业要求',
                'amount'        => '招聘人数',
                'recruitAmount' => '招聘人数',
            ],
            self::TYPE_COMPANY      => [
                'major'         => '专业要求',
                'recruitAmount' => '招聘人数',
                'amount'        => '招聘人数',
            ],
        ];

        if ($type && isset($options[$type])) {
            return $options[$type];
        }

        // 返回所有字段的合并
        $allFields = [];
        foreach ($options as $fields) {
            $allFields = array_merge($allFields, $fields);
        }

        return array_unique($allFields);
    }

    /**
     * 获取状态选项
     * @return array
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED  => '启用',
        ];
    }

    /**
     * 根据类型和目标ID获取配置
     * @param string $type
     * @param int    $targetId
     * @param string $platform
     * @return array
     */
    public static function getConfigs($type, $targetId, $platform = 'PC')
    {
        $query = static::find()
            ->where([
                'type'      => $type,
                'target_id' => $targetId,
                'status'    => self::STATUS_ENABLED,
            ])
            ->andWhere([
                'or',
                ['platform' => self::PLATFORM_ALL],
                ['platform' => $platform],
            ])
            ->andWhere([
                'or',
                ['start_time' => null],
                [
                    '<=',
                    'start_time',
                    date('Y-m-d H:i:s'),
                ],
            ])
            ->andWhere([
                'or',
                ['end_time' => null],
                [
                    '>=',
                    'end_time',
                    date('Y-m-d H:i:s'),
                ],
            ]);

        return $query->asArray()
            ->all();
    }

    /**
     * 批量获取多个目标ID的配置
     * @param string $type
     * @param array  $targetIds
     * @param string $platform
     * @return array
     */
    public static function getBatchConfigs($type, $targetIds, $platform = 'PC')
    {
        if (empty($targetIds)) {
            return [];
        }

        $query = static::find()
            ->where([
                'type'   => $type,
                'status' => self::STATUS_ENABLED,
            ])
            ->andWhere([
                'in',
                'target_id',
                $targetIds,
            ])
            ->andWhere([
                'or',
                ['platform' => self::PLATFORM_ALL],
                ['platform' => $platform],
            ])
            ->andWhere([
                'or',
                ['start_time' => null],
                [
                    '<=',
                    'start_time',
                    date('Y-m-d H:i:s'),
                ],
            ])
            ->andWhere([
                'or',
                ['end_time' => null],
                [
                    '>=',
                    'end_time',
                    date('Y-m-d H:i:s'),
                ],
            ]);

        $configs = $query->asArray()
            ->all();

        // 按target_id分组
        $result = [];
        foreach ($configs as $config) {
            $result[$config['target_id']][] = $config;
        }

        return $result;
    }

    /**
     * 应用配置到数据
     * @param array $data
     * @param array $configs
     * @return array
     */
    public static function applyConfigs($data, $configs)
    {
        if (empty($configs)) {
            return $data;
        }

        foreach ($configs as $config) {
            $fieldName  = $config['field_name'];
            $fieldValue = $config['field_value'];

            // 处理特殊字段
            switch ($fieldName) {
                case 'major':
                    // 专业字段可能是数组格式
                    if (isset($data['major']) && is_array($data['major'])) {
                        $data['major'] = [
                            [
                                'majorText' => $fieldValue,
                                'url'       => '#',
                            ],
                        ];
                    } else {
                        $data[$fieldName] = $fieldValue;
                    }
                    break;

                case 'majorTxt':
                    // 专业文本字段，可能需要追加
                    if (strpos($fieldValue, ',') === 0) {
                        // 以逗号开头表示追加
                        $data[$fieldName] = ($data[$fieldName] ?? '') . $fieldValue;
                    } else {
                        $data[$fieldName] = $fieldValue;
                    }
                    break;

                default:
                    $data[$fieldName] = $fieldValue;
                    break;
            }
        }

        return $data;
    }

    /**
     * 检查功能是否启用
     * @return bool
     */
    public static function isEnabled()
    {
        return 1;
    }
}
