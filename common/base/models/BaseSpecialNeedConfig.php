<?php

namespace common\base\models;

use common\models\SpecialNeedConfig;

/**
 * 特殊需求配置模型
 *
 * @property int    $id
 * @property string $name        配置名称
 * @property string $type        类型：company,announcement,job
 * @property int    $target_id   目标ID（单位ID/公告ID/职位ID）
 * @property string $field_name  字段名称
 * @property string $field_value 字段值
 * @property string $platform    适用平台：ALL,PC,H5,MINI
 * @property int    $status      状态：0=禁用，1=启用
 * @property string $start_time  生效开始时间
 * @property string $end_time    生效结束时间
 * @property string $remark      备注说明
 * @property int    $created_by  创建人ID
 * @property string $add_time    创建时间
 * @property string $update_time 更新时间
 */
class BaseSpecialNeedConfig extends SpecialNeedConfig
{
    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED  = 1;

    // 类型常量
    const TYPE_COMPANY      = 'company';
    const TYPE_ANNOUNCEMENT = 'announcement';
    const TYPE_JOB          = 'job';

    // 平台常量
    const PLATFORM_ALL  = 'ALL';
    const PLATFORM_PC   = 'PC';
    const PLATFORM_H5   = 'H5';
    const PLATFORM_MINI = 'MINI';

    /**
     * 字段映射配置 - 用于统一管理相同含义但不同key的字段
     * 格式：'主字段名' => ['实际字段名1', '实际字段名2', ...]
     *
     * 历史演进说明：
     * - major/majorTxt: major存储专业ID，majorTxt存储专业名称文本，统一使用major
     * - applyType/applyTypeText: applyType存储报名方式ID，applyTypeText存储报名方式名称文本，统一使用applyType
     * - amount/recruitAmount: 不同端使用不同字段名表示招聘人数，统一使用recruitAmount
     */
    const FIELD_MAPPING = [
        'recruitAmount' => ['amount', 'recruitAmount'],     // 招聘人数的统一映射
        'major'         => ['major', 'majorTxt'],          // 专业要求（ID和文本）
        'applyType'     => ['applyType', 'applyTypeText'], // 报名方式（ID和文本）
        'education'     => ['education'],                  // 学历要求
        'jobCategory'   => ['jobCategory'],                // 职位类别
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'special_need_config';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'name',
                    'type',
                    'target_id',
                    'field_name',
                ],
                'required',
            ],
            [
                [
                    'target_id',
                    'status',
                    'created_by',
                ],
                'integer',
            ],
            [
                [
                    'field_value',
                    'remark',
                ],
                'string',
            ],
            [
                [
                    'start_time',
                    'end_time',
                    'add_time',
                    'update_time',
                ],
                'safe',
            ],
            [
                ['name'],
                'string',
                'max' => 100,
            ],
            [
                [
                    'type',
                    'platform',
                ],
                'string',
                'max' => 20,
            ],
            [
                ['field_name'],
                'string',
                'max' => 50,
            ],
            [
                ['type'],
                'in',
                'range' => [
                    self::TYPE_COMPANY,
                    self::TYPE_ANNOUNCEMENT,
                    self::TYPE_JOB,
                ],
            ],
            [
                ['platform'],
                'in',
                'range' => [
                    self::PLATFORM_ALL,
                    self::PLATFORM_PC,
                    self::PLATFORM_H5,
                    self::PLATFORM_MINI,
                ],
            ],
            [
                ['status'],
                'in',
                'range' => [
                    self::STATUS_DISABLED,
                    self::STATUS_ENABLED,
                ],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'          => 'ID',
            'name'        => '配置名称',
            'type'        => '配置类型',
            'target_id'   => '目标ID',
            'field_name'  => '字段名称',
            'field_value' => '字段值',
            'platform'    => '适用平台',
            'status'      => '状态',
            'start_time'  => '生效开始时间',
            'end_time'    => '生效结束时间',
            'remark'      => '备注说明',
            'created_by'  => '创建人',
            'add_time'    => '创建时间',
            'update_time' => '更新时间',
        ];
    }

    /**
     * 获取类型选项
     * @return array
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_JOB          => '职位信息',
            self::TYPE_ANNOUNCEMENT => '公告信息',
            self::TYPE_COMPANY      => '单位信息',
        ];
    }

    /**
     * 获取平台选项
     * @return array
     */
    public static function getPlatformOptions()
    {
        return [
            self::PLATFORM_ALL  => '全平台',
            self::PLATFORM_PC   => 'PC端',
            self::PLATFORM_H5   => 'H5端',
            self::PLATFORM_MINI => '小程序',
        ];
    }

    /**
     * 获取字段选项（统一后的字段，避免重复）
     * @param string $type
     * @return array
     */
    public static function getFieldOptions($type = '')
    {
        // 统一的字段选项，避免重复和歧义
        // 注意：这里只显示主字段，不显示重复的字段（如majorTxt, applyTypeText, amount）
        $unifiedOptions = [
            self::TYPE_JOB          => [
                'education'     => '学历要求',
                'major'         => '专业要求',      // 统一major和majorTxt，使用major作为主字段
                'recruitAmount' => '招聘人数',      // 统一amount和recruitAmount
                'applyType'     => '报名方式',      // 统一applyType和applyTypeText，使用applyType作为主字段
                'jobCategory'   => '职位类别',
            ],
            self::TYPE_ANNOUNCEMENT => [
                'education'     => '学历要求',
                'major'         => '专业要求',      // 统一major和majorTxt
                'recruitAmount' => '招聘人数',      // 统一amount和recruitAmount
                'applyType'     => '报名方式',      // 统一applyType和applyTypeText
            ],
            self::TYPE_COMPANY      => [
                'major'         => '专业要求',      // 统一major和majorTxt
                'recruitAmount' => '招聘人数',      // 统一amount和recruitAmount
            ],
        ];

        if ($type && isset($unifiedOptions[$type])) {
            return $unifiedOptions[$type];
        }

        // 返回所有字段的合并（去重）
        $allFields = [];
        foreach ($unifiedOptions as $fields) {
            $allFields = array_merge($allFields, $fields);
        }

        return array_unique($allFields);
    }

    /**
     * 获取原始字段选项（保持向后兼容）
     * @param string $type
     * @return array
     */
    public static function getOriginalFieldOptions($type = '')
    {
        $options = [
            self::TYPE_JOB          => [
                'education'     => '学历要求',
                'major'         => '专业要求',
                'majorTxt'      => '专业要求文本',
                'amount'        => '招聘人数',
                'applyType'     => '报名方式',
                'applyTypeText' => '报名方式文本',
                'jobCategory'   => '职位类别',
            ],
            self::TYPE_ANNOUNCEMENT => [
                'education'     => '学历要求',
                'major'         => '专业要求',
                'amount'        => '招聘人数',
                'recruitAmount' => '招聘人数',
            ],
            self::TYPE_COMPANY      => [
                'major'         => '专业要求',
                'recruitAmount' => '招聘人数',
                'amount'        => '招聘人数',
            ],
        ];

        if ($type && isset($options[$type])) {
            return $options[$type];
        }

        // 返回所有字段的合并
        $allFields = [];
        foreach ($options as $fields) {
            $allFields = array_merge($allFields, $fields);
        }

        return array_unique($allFields);
    }

    /**
     * 获取状态选项
     * @return array
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED  => '启用',
        ];
    }

    /**
     * 根据类型和目标ID获取配置
     * @param string $type
     * @param int    $targetId
     * @param string $platform
     * @return array
     */
    public static function getConfigs($type, $targetId, $platform = 'PC')
    {
        $query = static::find()
            ->where([
                'type'      => $type,
                'target_id' => $targetId,
                'status'    => self::STATUS_ENABLED,
            ])
            ->andWhere([
                'or',
                ['platform' => self::PLATFORM_ALL],
                ['platform' => $platform],
            ])
            ->andWhere([
                'or',
                ['start_time' => null],
                [
                    '<=',
                    'start_time',
                    date('Y-m-d H:i:s'),
                ],
            ])
            ->andWhere([
                'or',
                ['end_time' => null],
                [
                    '>=',
                    'end_time',
                    date('Y-m-d H:i:s'),
                ],
            ]);

        return $query->asArray()
            ->all();
    }

    /**
     * 批量获取多个目标ID的配置
     * @param string $type
     * @param array  $targetIds
     * @param string $platform
     * @return array
     */
    public static function getBatchConfigs($type, $targetIds, $platform = 'PC')
    {
        if (empty($targetIds)) {
            return [];
        }

        $query = static::find()
            ->where([
                'type'   => $type,
                'status' => self::STATUS_ENABLED,
            ])
            ->andWhere([
                'in',
                'target_id',
                $targetIds,
            ])
            ->andWhere([
                'or',
                ['platform' => self::PLATFORM_ALL],
                ['platform' => $platform],
            ])
            ->andWhere([
                'or',
                ['start_time' => null],
                [
                    '<=',
                    'start_time',
                    date('Y-m-d H:i:s'),
                ],
            ])
            ->andWhere([
                'or',
                ['end_time' => null],
                [
                    '>=',
                    'end_time',
                    date('Y-m-d H:i:s'),
                ],
            ]);

        $configs = $query->asArray()
            ->all();

        // 按target_id分组
        $result = [];
        foreach ($configs as $config) {
            $result[$config['target_id']][] = $config;
        }

        return $result;
    }

    /**
     * 根据数据结构转换字段名
     * @param string $unifiedFieldName 统一字段名
     * @param array  $data             数据数组
     * @param string $type             配置类型
     * @return string 实际应该使用的字段名
     */
    public static function convertFieldName($unifiedFieldName, $data, $type = '')
    {
        // 如果不是需要转换的字段，直接返回
        if (!isset(self::FIELD_MAPPING[$unifiedFieldName])) {
            return $unifiedFieldName;
        }

        $possibleFields = self::FIELD_MAPPING[$unifiedFieldName];

        // 特殊处理招聘人数字段
        if ($unifiedFieldName === 'recruitAmount') {
            // 根据数据中存在的字段来决定使用哪个
            if (isset($data['amount'])) {
                return 'amount';
            } elseif (isset($data['recruitAmount'])) {
                return 'recruitAmount';
            } else {
                // 根据类型来决定默认使用的字段名
                switch ($type) {
                    case self::TYPE_JOB:
                        return 'amount';
                    case self::TYPE_ANNOUNCEMENT:
                    case self::TYPE_COMPANY:
                    default:
                        return 'recruitAmount';
                }
            }
        }

        // 特殊处理专业要求字段（major vs majorTxt）
        if ($unifiedFieldName === 'major') {
            // 根据数据中存在的字段来决定使用哪个
            // 如果同时存在major和majorTxt，优先使用majorTxt（更直观的文本字段）
            if (isset($data['majorTxt'])) {
                return 'majorTxt';
            } elseif (isset($data['major'])) {
                return 'major';
            } else {
                // 默认使用major字段（数据库存储的主字段）
                return 'major';
            }
        }

        // 特殊处理报名方式字段（applyType vs applyTypeText）
        if ($unifiedFieldName === 'applyType') {
            // 根据数据中存在的字段来决定使用哪个
            // 如果同时存在applyType和applyTypeText，优先使用applyTypeText（更直观的文本字段）
            if (isset($data['applyTypeText'])) {
                return 'applyTypeText';
            } elseif (isset($data['applyType'])) {
                return 'applyType';
            } else {
                // 默认使用applyType字段（数据库存储的主字段）
                return 'applyType';
            }
        }

        // 对于其他字段，返回第一个可能的字段名
        return $possibleFields[0];
    }

    /**
     * 应用配置到数据（保持向后兼容）
     * @param array $data
     * @param array $configs
     * @param string $type 配置类型，用于字段转换（可选）
     * @return array
     */
    public static function applyConfigs($data, $configs, $type = '')
    {
        if (empty($configs)) {
            return $data;
        }

        // 如果没有传递类型，尝试从配置中获取
        if (empty($type) && !empty($configs)) {
            $type = $configs[0]['type'] ?? '';
        }

        foreach ($configs as $config) {
            $unifiedFieldName = $config['field_name'];
            $fieldValue       = $config['field_value'];

            // 转换为实际的字段名
            $actualFieldName = self::convertFieldName($unifiedFieldName, $data, $type);

            // 处理特殊字段
            switch ($unifiedFieldName) {
                case 'major':
                    // 专业要求字段，统一处理major和majorTxt
                    // 先处理基本的字段更新
                    if (strpos($fieldValue, ',') === 0) {
                        // 以逗号开头表示追加
                        $data[$actualFieldName] = ($data[$actualFieldName] ?? '') . $fieldValue;
                        $finalValue = $data[$actualFieldName];
                    } else {
                        $data[$actualFieldName] = $fieldValue;
                        $finalValue = $fieldValue;
                    }

                    // 同步更新相关字段，确保数据一致性
                    if ($actualFieldName === 'majorTxt') {
                        // 如果更新的是majorTxt字段，同时更新major字段
                        if (isset($data['major'])) {
                            if (is_array($data['major'])) {
                                // H5端：major是数组格式
                                $data['major'] = [
                                    [
                                        'majorText' => $finalValue,
                                        'url'       => '#',
                                    ],
                                ];
                            } else {
                                // 小程序端：major是字符串格式
                                $data['major'] = $finalValue;
                            }
                        }
                    } elseif ($actualFieldName === 'major') {
                        // 如果更新的是major字段，同时更新majorTxt字段
                        if (isset($data['majorTxt'])) {
                            $data['majorTxt'] = $finalValue;
                        }

                        // 如果major字段本身是数组格式，需要特殊处理
                        if (isset($data['major']) && is_array($data['major'])) {
                            // H5端：更新数组内容
                            $data['major'] = [
                                [
                                    'majorText' => $finalValue,
                                    'url'       => '#',
                                ],
                            ];
                        }
                        // 小程序端的major字段是字符串，已经在上面的基本更新中处理了
                    }
                    break;

                case 'applyType':
                    // 报名方式字段，统一处理applyType和applyTypeText
                    // 先处理基本的字段更新
                    if (strpos($fieldValue, ',') === 0) {
                        // 以逗号开头表示追加
                        $data[$actualFieldName] = ($data[$actualFieldName] ?? '') . $fieldValue;
                        $finalValue = $data[$actualFieldName];
                    } else {
                        $data[$actualFieldName] = $fieldValue;
                        $finalValue = $fieldValue;
                    }

                    // 同步更新相关字段，确保数据一致性
                    if ($actualFieldName === 'applyTypeText') {
                        // 如果更新的是applyTypeText字段，同时更新applyType字段（如果存在）
                        if (isset($data['applyType'])) {
                            $data['applyType'] = $finalValue;
                        }
                    } elseif ($actualFieldName === 'applyType') {
                        // 如果更新的是applyType字段，同时更新applyTypeText字段（如果存在）
                        if (isset($data['applyTypeText'])) {
                            $data['applyTypeText'] = $finalValue;
                        }
                    }
                    break;

                case 'recruitAmount':
                    // 招聘人数字段，统一处理amount和recruitAmount
                    $data[$actualFieldName] = $fieldValue;
                    break;

                default:
                    $data[$actualFieldName] = $fieldValue;
                    break;
            }
        }

        return $data;
    }

    /**
     * 获取字段的显示名称（支持统一字段和原始字段）
     * @param string $fieldName 字段名
     * @param string $type      配置类型
     * @return string
     */
    public static function getFieldDisplayName($fieldName, $type = '')
    {
        // 先尝试从统一字段选项中获取
        $unifiedOptions = self::getFieldOptions($type);
        if (isset($unifiedOptions[$fieldName])) {
            return $unifiedOptions[$fieldName];
        }

        // 再尝试从原始字段选项中获取
        $originalOptions = self::getOriginalFieldOptions($type);
        if (isset($originalOptions[$fieldName])) {
            return $originalOptions[$fieldName];
        }

        // 如果都找不到，返回字段名本身
        return $fieldName;
    }

    /**
     * 检查字段是否为统一字段
     * @param string $fieldName
     * @return bool
     */
    public static function isUnifiedField($fieldName)
    {
        return isset(self::FIELD_MAPPING[$fieldName]);
    }

    /**
     * 获取统一字段对应的所有原始字段
     * @param string $unifiedFieldName
     * @return array
     */
    public static function getOriginalFieldNames($unifiedFieldName)
    {
        return self::FIELD_MAPPING[$unifiedFieldName] ?? [$unifiedFieldName];
    }

    /**
     * 检查功能是否启用
     * @return bool
     */
    public static function isEnabled()
    {
        return 1;
    }
}
