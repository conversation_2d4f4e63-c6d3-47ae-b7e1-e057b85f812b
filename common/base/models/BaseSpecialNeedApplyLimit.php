<?php

namespace common\base\models;

use common\exceptions\MessageException;
use common\models\SpecialNeedApplyLimit;
use Yii;
use yii\db\ActiveRecord;

/**
 * 投递限制配置模型
 *
 * @property int $id
 * @property string $name 限制名称
 * @property int $company_id 单位ID
 * @property string $limit_type 限制类型：count=次数限制,condition=条件限制
 * @property int $time_limit 时间限制（天）
 * @property int $count_limit 次数限制
 * @property string $condition_field 条件字段
 * @property string $condition_value 条件值
 * @property string $error_message 错误提示信息
 * @property int $status 状态：0=禁用，1=启用
 * @property string $remark 备注说明
 * @property int $created_by 创建人ID
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 */
class BaseSpecialNeedApplyLimit extends SpecialNeedApplyLimit
{
    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;
    
    // 限制类型常量
    const LIMIT_TYPE_COUNT = 'count';
    const LIMIT_TYPE_CONDITION = 'condition';
    
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'special_need_apply_limit';
    }
    
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'company_id', 'limit_type', 'error_message'], 'required'],
            [['company_id', 'time_limit', 'count_limit', 'status', 'created_by'], 'integer'],
            [['remark'], 'string'],
            [['add_time', 'update_time'], 'safe'],
            [['name'], 'string', 'max' => 100],
            [['limit_type'], 'string', 'max' => 20],
            [['condition_field'], 'string', 'max' => 50],
            [['condition_value'], 'string', 'max' => 100],
            [['error_message'], 'string', 'max' => 255],
            [['limit_type'], 'in', 'range' => [self::LIMIT_TYPE_COUNT, self::LIMIT_TYPE_CONDITION]],
            [['status'], 'in', 'range' => [self::STATUS_DISABLED, self::STATUS_ENABLED]],
        ];
    }
    
    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => '限制名称',
            'company_id' => '单位ID',
            'limit_type' => '限制类型',
            'time_limit' => '时间限制（天）',
            'count_limit' => '次数限制',
            'condition_field' => '条件字段',
            'condition_value' => '条件值',
            'error_message' => '错误提示信息',
            'status' => '状态',
            'remark' => '备注说明',
            'created_by' => '创建人',
            'add_time' => '创建时间',
            'update_time' => '更新时间',
        ];
    }
    
    /**
     * 获取限制类型选项
     * @return array
     */
    public static function getLimitTypeOptions()
    {
        return [
            self::LIMIT_TYPE_COUNT => '次数限制',
            self::LIMIT_TYPE_CONDITION => '条件限制',
        ];
    }
    
    /**
     * 获取条件字段选项
     * @return array
     */
    public static function getConditionFieldOptions()
    {
        return [
            'is_abroad' => '海外经历',
            'education_level' => '学历水平',
            'work_experience' => '工作经验',
        ];
    }
    
    /**
     * 获取状态选项
     * @return array
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED => '启用',
        ];
    }
    
    /**
     * 根据单位ID获取限制配置
     * @param int $companyId
     * @return array
     */
    public static function getByCompanyId($companyId)
    {
        return static::find()
            ->where([
                'company_id' => $companyId,
                'status' => self::STATUS_ENABLED
            ])
            ->asArray()
            ->all();
    }
    
    /**
     * 检查投递限制
     * @param int $jobId
     * @param int $companyId
     * @param int $announcementId
     * @param int $resumeId
     * @throws MessageException
     */
    public static function checkApplyLimit($jobId, $companyId, $announcementId, $resumeId)
    {
        // 检查功能是否启用
        if (!BaseSpecialNeedConfig::isEnabled()) {
            return;
        }
        
        $limits = static::getByCompanyId($companyId);
        
        if (empty($limits)) {
            return;
        }
        
        foreach ($limits as $limit) {
            static::checkSingleLimit($limit, $jobId, $companyId, $announcementId, $resumeId);
        }
    }
    
    /**
     * 检查单个限制规则
     * @param array $limit
     * @param int $jobId
     * @param int $companyId
     * @param int $announcementId
     * @param int $resumeId
     * @throws MessageException
     */
    protected static function checkSingleLimit($limit, $jobId, $companyId, $announcementId, $resumeId)
    {
        switch ($limit['limit_type']) {
            case self::LIMIT_TYPE_COUNT:
                static::checkCountLimit($limit, $companyId, $resumeId);
                break;
                
            case self::LIMIT_TYPE_CONDITION:
                static::checkConditionLimit($limit, $resumeId);
                break;
        }
    }
    
    /**
     * 检查次数限制
     * @param array $limit
     * @param int $companyId
     * @param int $resumeId
     * @throws MessageException
     */
    protected static function checkCountLimit($limit, $companyId, $resumeId)
    {
        $timeLimit = $limit['time_limit'];
        $countLimit = $limit['count_limit'];
        $errorMessage = $limit['error_message'];
        
        // 计算时间范围
        if ($timeLimit > 0) {
            $startTime = date('Y-m-d H:i:s', strtotime("-{$timeLimit} days"));
            $whereCondition = ['>=', 'add_time', $startTime];
        } else {
            // 如果时间限制为0，表示不限制时间，检查总次数
            $whereCondition = '1=1';
        }
        
        // 查询投递记录
        $count = BaseJobApplyRecord::find()
            ->where([
                'resume_id' => $resumeId,
                'company_id' => $companyId,
            ])
            ->andWhere($whereCondition)
            ->count();
        
        if ($count >= $countLimit) {
            // 记录限制触发日志
            static::logLimitTrigger($limit['id'], $companyId, $resumeId, 'count_limit', [
                'current_count' => $count,
                'limit_count' => $countLimit,
                'time_limit' => $timeLimit
            ]);
            
            throw new MessageException($errorMessage);
        }
    }
    
    /**
     * 检查条件限制
     * @param array $limit
     * @param int $resumeId
     * @throws MessageException
     */
    protected static function checkConditionLimit($limit, $resumeId)
    {
        $conditionField = $limit['condition_field'];
        $conditionValue = $limit['condition_value'];
        $errorMessage = $limit['error_message'];
        
        switch ($conditionField) {
            case 'is_abroad':
                $hasAbroad = BaseResumeEducation::find()
                    ->where([
                        'resume_id' => $resumeId,
                        'status' => BaseResumeEducation::STATUS_ACTIVE,
                        'is_abroad' => BaseResumeEducation::IS_ABROAD_YES,
                    ])
                    ->exists();
                
                if ($hasAbroad && $conditionValue == 'not_allowed') {
                    // 记录限制触发日志
                    static::logLimitTrigger($limit['id'], 0, $resumeId, 'condition_limit', [
                        'condition_field' => $conditionField,
                        'condition_value' => $conditionValue,
                        'actual_value' => 'has_abroad'
                    ]);
                    
                    throw new MessageException($errorMessage);
                }
                break;
                
            // 可以扩展其他条件字段
        }
    }
    
    /**
     * 记录限制触发日志
     * @param int $limitId
     * @param int $companyId
     * @param int $resumeId
     * @param string $limitType
     * @param array $details
     */
    protected static function logLimitTrigger($limitId, $companyId, $resumeId, $limitType, $details)
    {
        try {
            // 这里可以记录到日志表或者系统日志
            Yii::info("投递限制触发: limitId={$limitId}, companyId={$companyId}, resumeId={$resumeId}, type={$limitType}, details=" . json_encode($details), 'special_need_apply_limit');
        } catch (\Exception $e) {
            // 日志记录失败不影响主流程
        }
    }
    
    /**
     * 获取单位名称
     * @return \yii\db\ActiveQuery
     */
    public function getCompany()
    {
        return $this->hasOne(BaseCompany::class, ['id' => 'company_id']);
    }
}
