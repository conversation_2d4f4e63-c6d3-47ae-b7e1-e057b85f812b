<?php

namespace common\base\models;

use common\models\AdminMessage;
use Yii;
use yii\db\Exception;

class BaseAdminMessage extends AdminMessage
{
    const TYPE_COMPANY_AUDIT_PASS = 1;      //企业审核通过

    const IS_DELETE_YES = 1;       //已删除
    const IS_DELETE_NO  = 0;      //未删除

    const READ_YES = 1;       //已读
    const READ_NO  = 0;      //未读

    const TYPE_NAME = [
        self::TYPE_COMPANY_AUDIT_PASS => '【审核通过】',
    ];

    /**
     * 发送站内消息
     * @param        $admin_id
     * @param        $type
     * @param        $title
     * @param        $content
     * @param string $inner_link
     * @param int    $inner_link_params
     * @return string|void
     */
    public static function send(
        $admin_id,
        $type,
        $title,
        $content,
        string $inner_link = '',
        int $inner_link_params = 0
    ) {
        $data           = [];
        $messageSaveKey = [
            'admin_id',
            'type',
            'title',
            'content',
            'inner_link',
            'inner_link_params',
        ];

        //将用户id改为数组格式
        $admin_ids = is_array($admin_id) ? $admin_id : [$admin_id];
        foreach ($admin_ids as $key => $admin_id) {
            $data[$key] = [
                'admin_id'          => $admin_id,
                'type'              => $type,
                'title'             => $title,
                'content'           => $content,
                'inner_link'        => $inner_link,
                'inner_link_params' => $inner_link_params,
            ];
        }

        $messageModel = Yii::$app->db;
        $transaction  = Yii::$app->db->beginTransaction();
        try {
            $messageModel->createCommand()
                ->batchInsert(self::tableName(), $messageSaveKey, $data)
                ->execute();

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $e->getMessage();
        }
    }

    /**
     * 根据条件获取消息列表
     * @param $where
     * @param $select
     * @return array
     */
    public static function findList($where, $select): array
    {
        return self::find()
            ->where([
                $where,
            ])
            ->select($select)
            ->asArray()
            ->all();
    }

    /**
     * 根据条件获取数量
     * @param $where
     * @return bool|int|string|null
     */
    public static function getTypeCount($where)
    {
        return self::find()
            ->where($where)
            ->count();
    }

}