CREATE TABLE `seo_job_wiki` (
                                `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                `code` varchar(32) NOT NULL COMMENT '唯一码',
                                `keyword` varchar(128) NOT NULL DEFAULT '' COMMENT '关键字',
                                `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
                                `job_introduction` varchar(800) NOT NULL DEFAULT '' COMMENT '职位简介',
                                `job_content` varchar(2000) NOT NULL DEFAULT '' COMMENT '职位内容',
                                `jump_link` varchar(255) NOT NULL DEFAULT '' COMMENT '跳转链接',
                                `is_delete` tinyint(2) NOT NULL DEFAULT '0' COMMENT '0:正常；1:删除',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `idx_code` (`code`) USING BTREE,
                                KEY `idx_keyword` (`keyword`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='职位百科';