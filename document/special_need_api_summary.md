# 特殊需求配置系统 API 接口汇总（小驼峰版）

## 📋 接口列表

### 字段替换配置接口
| 功能 | 方法 | 接口地址 | 说明 |
|------|------|----------|------|
| 获取配置列表 | GET | `/admin/specialNeed/configIndex` | 支持分页和搜索 |
| 查看配置详情 | GET | `/admin/specialNeed/configView?id={id}` | 获取单个配置信息 |
| 创建配置 | POST | `/admin/specialNeed/configCreate` | 创建新的字段替换配置 |
| 更新配置 | POST | `/admin/specialNeed/configUpdate?id={id}` | 更新现有配置 |
| 删除配置 | POST | `/admin/specialNeed/configDelete?id={id}` | 删除指定配置 |
| 批量操作 | POST | `/admin/specialNeed/configBatchStatus` | 批量启用/禁用配置 |
| 获取字段选项 | GET | `/admin/specialNeed/getFieldOptions?type={type}` | 根据类型获取可用字段 |

### 投递限制配置接口
| 功能 | 方法 | 接口地址 | 说明 |
|------|------|----------|------|
| 获取限制列表 | GET | `/admin/specialNeed/limitIndex` | 支持分页和搜索 |
| 查看限制详情 | GET | `/admin/specialNeed/limitView?id={id}` | 获取单个限制信息 |
| 创建限制 | POST | `/admin/specialNeed/limitCreate` | 创建新的投递限制 |
| 更新限制 | POST | `/admin/specialNeed/limitUpdate?id={id}` | 更新现有限制 |
| 删除限制 | POST | `/admin/specialNeed/limitDelete?id={id}` | 删除指定限制 |

### 工具接口
| 功能 | 方法 | 接口地址 | 说明 |
|------|------|----------|------|
| 测试配置 | POST | `/admin/specialNeed/testConfig` | 测试配置是否生效 |
| 导出配置 | GET | `/admin/specialNeed/exportConfig` | 导出配置为JSON文件 |
| 导入配置 | POST | `/admin/specialNeed/importConfig` | 从JSON文件导入配置 |

## 🔧 统一返回格式

```javascript
// 成功响应
{
  "msg": "操作成功信息",
  "result": 1,
  "data": {
    // 具体数据
  }
}

// 失败响应
{
  "msg": "错误信息",
  "result": 0,
  "data": null
}
```

## 📊 列表数据格式

```javascript
{
  "msg": "",
  "result": 1,
  "data": {
    "list": [
      // 数据项数组
    ],
    "pages": {
      "total": 50,    // 总记录数
      "limit": 20,    // 每页数量
      "page": 1       // 当前页码
    }
  }
}
```

## 📝 请求参数格式

### 创建/更新配置
```javascript
{
  "SpecialNeedConfig": {
    "name": "配置名称",
    "type": "job|announcement|company",
    "target_id": 123456,
    "field_name": "字段名称",
    "field_value": "字段值",
    "platform": "ALL|PC|H5|MINI",
    "status": 1,
    "remark": "备注"
  }
}
```

### 创建/更新投递限制
```javascript
{
  "SpecialNeedApplyLimit": {
    "name": "限制名称",
    "company_id": 123456,
    "limit_type": "count|condition",
    "time_limit": 15,           // 次数限制时使用
    "count_limit": 1,           // 次数限制时使用
    "condition_field": "is_abroad",  // 条件限制时使用
    "condition_value": "not_allowed", // 条件限制时使用
    "error_message": "错误提示信息",
    "status": 1,
    "remark": "备注"
  }
}
```

## 🎯 快速使用示例

### 1. 获取配置列表
```bash
GET /admin/specialNeed/configIndex?page=1&pageSize=20&type=job
```

### 2. 创建职位专业修改
```bash
POST /admin/specialNeed/configCreate
Content-Type: application/json

{
  "SpecialNeedConfig": {
    "name": "电子科技大学专业修改",
    "type": "job",
    "target_id": 301480,
    "field_name": "major",
    "field_value": "马克思主义理论、心理学或学校其他学科专业背景",
    "platform": "ALL"
  }
}
```

### 3. 创建投递次数限制
```bash
POST /admin/specialNeed/limitCreate
Content-Type: application/json

{
  "SpecialNeedApplyLimit": {
    "name": "中科院金属所投递限制",
    "company_id": 89616,
    "limit_type": "count",
    "time_limit": 15,
    "count_limit": 1,
    "error_message": "您已超出该单位简历投递次数限制，无法再行投递"
  }
}
```

### 4. 批量启用配置
```bash
POST /admin/specialNeed/configBatchStatus
Content-Type: application/json

{
  "ids": [1, 2, 3],
  "status": 1
}
```

### 5. 获取字段选项
```bash
GET /admin/specialNeed/getFieldOptions?type=job
```

### 6. 测试配置
```bash
POST /admin/specialNeed/testConfig
Content-Type: application/json

{
  "type": "job",
  "target_id": 301480,
  "platform": "PC"
}
```

## 📚 数据字典

### 配置类型 (type)
- `job` - 职位信息
- `announcement` - 公告信息
- `company` - 单位信息

### 字段名称 (field_name)
- `major` - 专业要求
- `education` - 学历要求
- `amount` - 招聘人数
- `applyType` - 报名方式
- `jobCategory` - 职位类别

### 平台类型 (platform)
- `ALL` - 全平台
- `PC` - PC端
- `H5` - H5端
- `MINI` - 小程序

### 限制类型 (limit_type)
- `count` - 次数限制
- `condition` - 条件限制

### 状态 (status)
- `0` - 禁用
- `1` - 启用

## ⚠️ 注意事项

1. **接口命名**: 所有接口使用小驼峰命名规范
2. **参数包装**: 创建和更新时需要包装在对应的模型名称下
3. **返回格式**: 统一使用 `{msg, result, data}` 格式
4. **分页格式**: 使用 `{total, limit, page}` 格式
5. **错误处理**: `result=0` 时查看 `msg` 字段获取错误信息
6. **事务处理**: 所有写操作都包含事务处理，确保数据一致性

## 🔗 相关文档

- [完整API文档](./special_need_api_docs.md)
- [快速参考文档](./special_need_api_quick_reference.md)
- [使用指南](./special_need_usage_guide.md)
