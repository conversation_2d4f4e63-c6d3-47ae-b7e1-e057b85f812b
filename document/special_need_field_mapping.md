# 特殊需求字段映射功能说明

## 概述

为了解决在特殊页面逻辑配置中，不同端使用不同key但表示相同含义的字段问题（如`amount`和`recruitAmount`都表示招聘人数），我们引入了字段映射机制，统一管理这些字段，避免在前端编辑时出现重复选项。

## 问题背景

在原有系统中，存在以下问题：
- 职位信息使用`amount`字段表示招聘人数
- 公告信息和单位信息使用`recruitAmount`字段表示招聘人数
- 在前端配置界面中，用户会看到两个选项都表示"招聘人数"，造成困惑

## 解决方案

### 1. 字段映射配置

在`BaseSpecialNeedConfig`类中添加了`FIELD_MAPPING`常量，定义字段映射关系：

```php
const FIELD_MAPPING = [
    'recruitAmount' => ['amount', 'recruitAmount'], // 招聘人数的统一映射
    'major'         => ['major'],                   // 专业要求
    'majorTxt'      => ['majorTxt'],               // 专业要求文本
    'education'     => ['education'],              // 学历要求
    'applyType'     => ['applyType'],              // 报名方式
    'applyTypeText' => ['applyTypeText'],          // 报名方式文本
    'jobCategory'   => ['jobCategory'],            // 职位类别
];
```

### 2. 统一字段选项

修改了`getFieldOptions`方法，现在返回统一的字段选项：

```php
// 职位信息
'recruitAmount' => '招聘人数',  // 统一使用recruitAmount

// 公告信息  
'recruitAmount' => '招聘人数',  // 统一使用recruitAmount

// 单位信息
'recruitAmount' => '招聘人数',  // 统一使用recruitAmount
```

### 3. 字段转换机制

添加了`convertFieldName`方法，在应用配置时自动转换为正确的字段名：

```php
public static function convertFieldName($unifiedFieldName, $data, $type = '')
```

转换逻辑：
- 对于`recruitAmount`字段，根据数据中存在的字段来决定使用`amount`还是`recruitAmount`
- 如果数据中存在`amount`字段，则使用`amount`
- 如果数据中存在`recruitAmount`字段，则使用`recruitAmount`
- 根据配置类型决定默认字段名（职位默认用`amount`，公告和单位默认用`recruitAmount`）

## 新增方法

### 1. getFieldDisplayName($fieldName, $type = '')
获取字段的显示名称，支持统一字段和原始字段。

### 2. isUnifiedField($fieldName)
检查字段是否为统一字段。

### 3. getOriginalFieldNames($unifiedFieldName)
获取统一字段对应的所有原始字段。

### 4. getOriginalFieldOptions($type = '')
获取原始字段选项（保持向后兼容）。

## 向后兼容性

- 保持了原有的`applyConfigs`方法签名兼容性
- 添加了`getOriginalFieldOptions`方法，保留原始字段选项
- 现有的配置数据无需修改，系统会自动处理字段转换

## 使用示例

### 前端配置界面
现在用户在配置界面只会看到：
- 招聘人数（统一选项，不再有重复）
- 学历要求
- 专业要求
- 等其他字段

### 配置应用
当用户选择"招聘人数"并设置值为"若干"时：
- 对于职位数据，系统会自动应用到`amount`字段
- 对于公告数据，系统会自动应用到`recruitAmount`字段
- 对于单位数据，系统会自动应用到`recruitAmount`字段

## 测试

可以运行`test_special_need_field_mapping.php`文件来测试功能是否正常工作。

## 扩展

如果将来需要添加更多的字段映射，只需要在`FIELD_MAPPING`常量中添加相应的映射关系即可。

例如，如果需要统一"学科"字段：
```php
const FIELD_MAPPING = [
    'recruitAmount' => ['amount', 'recruitAmount'],
    'subject'       => ['subject', 'discipline', 'major_category'], // 新增学科字段映射
    // ... 其他映射
];
```
