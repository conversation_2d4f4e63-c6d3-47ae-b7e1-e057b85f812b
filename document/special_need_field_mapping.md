# 特殊需求字段映射功能说明

## 概述

为了解决在特殊页面逻辑配置中，不同端使用不同key但表示相同含义的字段问题（如`amount`和`recruitAmount`都表示招聘人数），我们引入了字段映射机制，统一管理这些字段，避免在前端编辑时出现重复选项。

## 问题背景

在原有系统中，由于历史演进和多人维护，存在以下重复和不一致的字段问题：

### 1. 招聘人数字段重复
- 职位信息使用`amount`字段表示招聘人数
- 公告信息和单位信息使用`recruitAmount`字段表示招聘人数
- 在前端配置界面中，用户会看到两个选项都表示"招聘人数"，造成困惑

### 2. 专业要求字段混乱
- `major`字段：存储专业ID（如：`"120,121,122"`）
- `majorTxt`字段：存储专业名称文本（如：`"计算机科学与技术,软件工程"`）
- 两个字段表示同一概念的不同形式，但在配置界面都显示为"专业要求"

### 3. 报名方式字段重复
- `applyType`字段：存储报名方式ID（如：`"1,2,3"`）
- `applyTypeText`字段：存储报名方式名称文本（如：`"电子邮件,现场报名"`）
- 同样造成配置界面的混乱

### 历史演进原因
1. **早期设计**：最初只有ID字段（`major`, `applyType`）
2. **性能优化**：为避免频繁查询字典表，后来添加了Text字段
3. **多人维护**：不同开发者可能不知道已有字段，重复创建
4. **需求变更**：前端显示需求变化，需要同时支持ID和文本格式

## 解决方案

### 1. 字段映射配置

在`BaseSpecialNeedConfig`类中添加了`FIELD_MAPPING`常量，定义字段映射关系：

```php
const FIELD_MAPPING = [
    'recruitAmount' => ['amount', 'recruitAmount'],     // 招聘人数的统一映射
    'major'         => ['major', 'majorTxt'],          // 专业要求（ID和文本）
    'applyType'     => ['applyType', 'applyTypeText'], // 报名方式（ID和文本）
    'education'     => ['education'],                  // 学历要求
    'jobCategory'   => ['jobCategory'],                // 职位类别
];
```

### 字段映射说明

1. **recruitAmount**: 统一`amount`和`recruitAmount`两个表示招聘人数的字段
2. **major**: 统一`major`（专业ID）和`majorTxt`（专业文本）两个字段，使用`major`作为主字段
3. **applyType**: 统一`applyType`（报名方式ID）和`applyTypeText`（报名方式文本）两个字段，使用`applyType`作为主字段

### 数据库兼容性

- 数据库中的`field_name`字段存储的是主字段名（如`major`, `applyType`, `recruitAmount`）
- 前端API接口只返回主字段，不显示重复的字段选项
- 系统在应用配置时，会根据实际数据结构智能选择对应的字段

### 2. 统一字段选项

修改了`getFieldOptions`方法，现在返回统一的字段选项：

```php
// 职位信息
'recruitAmount' => '招聘人数',  // 统一使用recruitAmount

// 公告信息  
'recruitAmount' => '招聘人数',  // 统一使用recruitAmount

// 单位信息
'recruitAmount' => '招聘人数',  // 统一使用recruitAmount
```

### 3. 字段转换机制

添加了`convertFieldName`方法，在应用配置时自动转换为正确的字段名：

```php
public static function convertFieldName($unifiedFieldName, $data, $type = '')
```

转换逻辑：
- 对于`recruitAmount`字段，根据数据中存在的字段来决定使用`amount`还是`recruitAmount`
- 如果数据中存在`amount`字段，则使用`amount`
- 如果数据中存在`recruitAmount`字段，则使用`recruitAmount`
- 根据配置类型决定默认字段名（职位默认用`amount`，公告和单位默认用`recruitAmount`）

## 新增方法

### 1. getFieldDisplayName($fieldName, $type = '')
获取字段的显示名称，支持统一字段和原始字段。

### 2. isUnifiedField($fieldName)
检查字段是否为统一字段。

### 3. getOriginalFieldNames($unifiedFieldName)
获取统一字段对应的所有原始字段。

### 4. getOriginalFieldOptions($type = '')
获取原始字段选项（保持向后兼容）。

## 向后兼容性

- 保持了原有的`applyConfigs`方法签名兼容性
- 添加了`getOriginalFieldOptions`方法，保留原始字段选项
- 现有的配置数据无需修改，系统会自动处理字段转换

## 使用示例

### 前端配置界面
现在用户在配置界面只会看到：
- 专业要求（统一选项，不再有major和majorTxt的重复）
- 报名方式（统一选项，不再有applyType和applyTypeText的重复）
- 招聘人数（统一选项，不再有amount和recruitAmount的重复）
- 学历要求
- 职位类别

### 配置应用
**专业要求配置**：
- 用户选择"专业要求"字段，数据库存储为`major`
- 对于职位数据，系统应用到`major`字段
- 对于公告数据，系统应用到`majorTxt`字段

**报名方式配置**：
- 用户选择"报名方式"字段，数据库存储为`applyType`
- 对于职位数据，系统应用到`applyType`字段
- 对于公告数据，系统应用到`applyTypeText`字段

**招聘人数配置**：
- 用户选择"招聘人数"字段，数据库存储为`recruitAmount`
- 对于职位数据，系统应用到`amount`字段
- 对于公告数据，系统应用到`recruitAmount`字段

## 测试

可以运行`test_special_need_field_mapping.php`文件来测试功能是否正常工作。

## 扩展

如果将来需要添加更多的字段映射，只需要在`FIELD_MAPPING`常量中添加相应的映射关系即可。

例如，如果需要统一"学科"字段：
```php
const FIELD_MAPPING = [
    'recruitAmount' => ['amount', 'recruitAmount'],
    'subject'       => ['subject', 'discipline', 'major_category'], // 新增学科字段映射
    // ... 其他映射
];
```
