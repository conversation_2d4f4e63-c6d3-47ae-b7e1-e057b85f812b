-- 特殊需求功能模块数据库表结构

-- 特殊需求规则表
CREATE TABLE `special_need_rule` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '规则名称',
  `description` text COMMENT '规则描述',
  `service_type` varchar(50) NOT NULL DEFAULT '' COMMENT '服务类型：job_info,company_info,announcement_info,job_apply',
  `method_name` varchar(50) NOT NULL DEFAULT '' COMMENT '方法名称',
  `trigger_conditions` json COMMENT '触发条件配置',
  `actions` json COMMENT '执行动作配置',
  `platforms` varchar(50) NOT NULL DEFAULT 'PC,H5,MINI' COMMENT '适用平台',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_service_method` (`service_type`, `method_name`),
  KEY `idx_status_time` (`status`, `start_time`, `end_time`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='特殊需求规则配置表';

-- 规则执行日志表
CREATE TABLE `special_need_rule_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `rule_id` int(11) NOT NULL DEFAULT 0 COMMENT '规则ID',
  `trigger_data` json COMMENT '触发时的数据',
  `execution_result` json COMMENT '执行结果',
  `execution_time` decimal(10,4) NOT NULL DEFAULT 0.0000 COMMENT '执行耗时(秒)',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '执行状态：0=失败，1=成功',
  `error_message` text COMMENT '错误信息',
  `platform` varchar(20) NOT NULL DEFAULT '' COMMENT '执行平台',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
  PRIMARY KEY (`id`),
  KEY `idx_rule_time` (`rule_id`, `add_time`),
  KEY `idx_status` (`status`),
  KEY `idx_platform` (`platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='特殊需求规则执行日志表';

-- 规则模板表
CREATE TABLE `special_need_rule_template` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '模板名称',
  `description` text COMMENT '模板描述',
  `service_type` varchar(50) NOT NULL DEFAULT '' COMMENT '服务类型',
  `template_config` json COMMENT '模板配置',
  `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_service_type` (`service_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='特殊需求规则模板表';

-- 插入一些示例规则模板
INSERT INTO `special_need_rule_template` (`name`, `description`, `service_type`, `template_config`) VALUES
('职位信息字段替换', '用于替换职位详情页面的字段值', 'job_info', '{
  "trigger_conditions": {
    "type": "AND",
    "conditions": [
      {
        "field": "announcementId",
        "operator": "=",
        "value": "",
        "data_type": "number"
      }
    ]
  },
  "actions": {
    "type": "field_replace",
    "actions": [
      {
        "action_type": "replace_field",
        "target_field": "education",
        "new_value": "",
        "platform_specific": {
          "PC": "",
          "H5": "",
          "MINI": ""
        }
      }
    ]
  }
}'),

('投递限制规则', '用于限制用户对特定单位的投递次数', 'job_apply', '{
  "trigger_conditions": {
    "type": "AND",
    "conditions": [
      {
        "field": "companyId",
        "operator": "=",
        "value": "",
        "data_type": "number"
      }
    ]
  },
  "actions": {
    "type": "validation",
    "actions": [
      {
        "action_type": "validation",
        "validation_type": "apply_limit",
        "validation_config": {
          "time_limit": 15,
          "time_unit": "days",
          "max_count": 1,
          "error_message": "您已超出该单位简历投递次数限制，无法再行投递"
        }
      }
    ]
  }
}'),

('公告职位列表字段修改', '用于修改公告页面职位列表的字段显示', 'announcement_info', '{
  "trigger_conditions": {
    "type": "AND",
    "conditions": [
      {
        "field": "announcementId",
        "operator": "=",
        "value": "",
        "data_type": "number"
      }
    ]
  },
  "actions": {
    "type": "field_replace",
    "actions": [
      {
        "action_type": "replace_field",
        "target_field": "major",
        "new_value": "详见招聘简章",
        "platform_specific": {}
      }
    ]
  }
}');

-- 插入系统配置，用于控制特殊需求功能的总开关
INSERT INTO `system_config` (`key`, `value`, `description`) VALUES
('special_need_enabled', '1', '特殊需求功能总开关：0=关闭，1=开启'),
('special_need_log_enabled', '1', '特殊需求执行日志开关：0=关闭，1=开启'),
('special_need_cache_ttl', '3600', '特殊需求规则缓存时间（秒）');
