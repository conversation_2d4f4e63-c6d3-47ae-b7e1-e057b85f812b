# 特殊需求配置系统使用指南

## 系统概述

特殊需求配置系统让您可以通过简单的表单配置来替换职位、公告、单位信息的字段内容，以及设置投递限制规则，无需修改代码。

## 功能特点

1. **简单易用**：通过表单填写即可完成配置
2. **实时生效**：配置保存后立即生效
3. **平台支持**：支持PC、H5、小程序三端
4. **时间控制**：支持设置生效时间范围
5. **向后兼容**：与现有硬编码逻辑并存

## 使用场景

### 1. 字段内容替换
- 修改职位的专业要求、学历要求、招聘人数等
- 修改公告的专业要求、招聘人数等
- 修改单位页面的专业要求、招聘人数等

### 2. 投递限制
- 限制用户对特定单位的投递次数
- 根据用户条件限制投递（如海外经历）

## 配置步骤

### 字段替换配置

#### 步骤1：进入管理界面
访问：`/admin/special-need/config-index`

#### 步骤2：创建新配置
点击"新增配置"按钮

#### 步骤3：填写配置信息
- **配置名称**：给配置起个容易识别的名称，如"电子科技大学专业修改"
- **配置类型**：选择要修改的内容类型
  - 职位信息：修改职位详情页的字段
  - 公告信息：修改公告页面的字段
  - 单位信息：修改单位页面的字段
- **目标ID**：输入要影响的ID
  - 职位信息：输入职位ID
  - 公告信息：输入公告ID
  - 单位信息：输入单位ID
- **字段名称**：选择要修改的字段
  - education：学历要求
  - major：专业要求
  - amount：招聘人数
  - applyType：报名方式
- **字段值**：输入新的内容
- **适用平台**：选择在哪些平台生效
  - 全平台：PC、H5、小程序都生效
  - PC端：只在PC端生效
  - H5端：只在H5端生效
  - 小程序：只在小程序生效
- **生效时间**：可选，设置配置的生效时间范围

#### 步骤4：保存配置
点击"保存"按钮完成配置

### 投递限制配置

#### 步骤1：进入限制管理
访问：`/admin/special-need/limit-index`

#### 步骤2：创建新限制
点击"新增投递限制"按钮

#### 步骤3：填写限制信息
- **限制名称**：给限制起个名称，如"中科院金属所投递限制"
- **单位ID**：输入要限制的单位ID
- **限制类型**：选择限制类型
  - 次数限制：限制投递次数
  - 条件限制：根据用户条件限制
- **时间限制**：如果是次数限制，设置时间范围（天数）
- **次数限制**：如果是次数限制，设置最大次数
- **条件字段**：如果是条件限制，选择判断字段
- **条件值**：如果是条件限制，设置条件值
- **错误提示**：用户触发限制时显示的错误信息

#### 步骤4：保存限制
点击"保存"按钮完成配置

## 配置示例

### 示例1：修改职位专业要求

**场景**：职位ID为301480的职位，需要将专业要求改为"马克思主义理论、心理学或学校其他学科专业背景"

**配置**：
- 配置名称：电子科技大学专业修改
- 配置类型：职位信息
- 目标ID：301480
- 字段名称：major
- 字段值：马克思主义理论、心理学或学校其他学科专业背景
- 适用平台：全平台

### 示例2：修改招聘人数

**场景**：职位ID为301480的职位，需要将招聘人数改为"不超过15"

**配置**：
- 配置名称：电子科技大学人数修改
- 配置类型：职位信息
- 目标ID：301480
- 字段名称：amount
- 字段值：不超过15
- 适用平台：全平台

### 示例3：投递次数限制

**场景**：单位ID为89616的单位，15天内只能投递1次

**配置**：
- 限制名称：中科院金属所投递限制
- 单位ID：89616
- 限制类型：次数限制
- 时间限制：15
- 次数限制：1
- 错误提示：您已超出该单位简历投递次数限制，无法再行投递

### 示例4：条件限制

**场景**：单位ID为60的单位，有海外经历的用户不能投递

**配置**：
- 限制名称：四川工商职业技术学院海外限制
- 单位ID：60
- 限制类型：条件限制
- 条件字段：is_abroad
- 条件值：not_allowed
- 错误提示：对不起，您暂不符合招聘要求，建议尝试其他机会！

## 管理功能

### 配置管理
- **查看列表**：查看所有配置，支持搜索和筛选
- **编辑配置**：修改现有配置
- **启用/禁用**：快速启用或禁用配置
- **批量操作**：批量启用/禁用多个配置
- **导入/导出**：支持配置的导入导出

### 测试功能
- **配置测试**：输入类型和ID，查看匹配的配置
- **实时预览**：查看配置应用后的效果

### 日志查看
- **执行日志**：查看配置的执行情况
- **错误日志**：查看配置执行中的错误

## 注意事项

1. **ID准确性**：确保输入的职位ID、公告ID、单位ID准确无误
2. **字段名称**：选择正确的字段名称，不同类型支持的字段不同
3. **平台差异**：不同平台的字段名称可能略有差异，系统会自动处理
4. **生效时间**：配置保存后立即生效，请谨慎操作
5. **优先级**：新配置系统优先级高于原有硬编码逻辑
6. **备份**：重要配置建议导出备份

## 技术支持

如遇到问题，请联系技术支持团队，并提供：
1. 配置的详细信息
2. 预期效果和实际效果
3. 相关的职位/公告/单位ID
4. 错误信息截图
