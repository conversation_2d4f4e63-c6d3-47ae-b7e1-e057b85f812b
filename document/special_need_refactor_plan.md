# 特殊需求功能模块重构方案

## 项目背景
当前 `common/service/specialNeedService/` 目录下的特殊需求处理逻辑都是硬编码的，每次需要添加或修改特殊需求时都需要修改代码，维护成本高。需要将其重构为可配置的功能模块。

## 现状分析

### 现有文件结构
- `BaseService.php` - 基础服务类，控制特殊需求开关
- `JobInformationService.php` - 职位信息特殊处理
- `CompanyInformationService.php` - 公司信息特殊处理  
- `AnnouncementInformationService.php` - 公告信息特殊处理
- `JobApplyService.php` - 求职申请特殊限制

### 现有问题
1. 所有规则硬编码在PHP代码中
2. 每次修改需要发布代码
3. 配置分散，难以统一管理
4. 缺乏可视化管理界面
5. 规则复杂度高，维护困难

## 重构目标
1. 将硬编码规则转换为数据库配置
2. 提供后台管理界面进行规则配置
3. 支持动态启用/禁用规则
4. 保持向后兼容性
5. 提供规则模板和导入导出功能

## 技术方案

### 1. 数据库设计

#### 特殊需求规则表 (special_need_rule)
```sql
CREATE TABLE `special_need_rule` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '规则名称',
  `description` text COMMENT '规则描述',
  `service_type` varchar(50) NOT NULL DEFAULT '' COMMENT '服务类型：job_info,company_info,announcement_info,job_apply',
  `method_name` varchar(50) NOT NULL DEFAULT '' COMMENT '方法名称',
  `trigger_conditions` json COMMENT '触发条件配置',
  `actions` json COMMENT '执行动作配置',
  `platforms` varchar(50) NOT NULL DEFAULT 'PC,H5,MINI' COMMENT '适用平台',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_service_method` (`service_type`, `method_name`),
  KEY `idx_status_time` (`status`, `start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='特殊需求规则配置表';
```

#### 规则执行日志表 (special_need_rule_log)
```sql
CREATE TABLE `special_need_rule_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `rule_id` int(11) NOT NULL DEFAULT 0 COMMENT '规则ID',
  `trigger_data` json COMMENT '触发时的数据',
  `execution_result` json COMMENT '执行结果',
  `execution_time` decimal(10,4) NOT NULL DEFAULT 0.0000 COMMENT '执行耗时(秒)',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '执行状态：0=失败，1=成功',
  `error_message` text COMMENT '错误信息',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
  PRIMARY KEY (`id`),
  KEY `idx_rule_time` (`rule_id`, `add_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='特殊需求规则执行日志表';
```

### 2. 配置数据结构

#### 触发条件配置 (trigger_conditions)
```json
{
  "type": "AND|OR",
  "conditions": [
    {
      "field": "announcementId|companyId|jobId|resumeId",
      "operator": "=|!=|>|<|>=|<=|in|not_in|like|not_like",
      "value": "具体值或数组",
      "data_type": "string|number|array"
    }
  ]
}
```

#### 执行动作配置 (actions)
```json
{
  "type": "field_replace|field_add|validation|custom",
  "actions": [
    {
      "action_type": "replace_field",
      "target_field": "education|major|amount|applyType",
      "new_value": "新值",
      "platform_specific": {
        "PC": "PC端特定值",
        "H5": "H5端特定值", 
        "MINI": "小程序端特定值"
      }
    },
    {
      "action_type": "add_field",
      "target_field": "majorTxt",
      "append_value": ",新增专业",
      "separator": ","
    },
    {
      "action_type": "validation",
      "validation_type": "apply_limit",
      "validation_config": {
        "time_limit": 15,
        "time_unit": "days",
        "max_count": 1,
        "error_message": "您已超出该单位简历投递次数限制，无法再行投递"
      }
    }
  ]
}
```

### 3. 核心类设计

#### 规则引擎类 (SpecialNeedRuleEngine)
- 负责加载和执行规则
- 条件匹配判断
- 动作执行
- 日志记录

#### 规则管理类 (SpecialNeedRuleManager)  
- 规则的CRUD操作
- 规则缓存管理
- 规则导入导出
- 规则模板管理

#### 重构后的Service类
- 保持现有接口不变
- 内部调用规则引擎
- 向后兼容现有硬编码逻辑

### 4. 管理界面功能
1. 规则列表管理（增删改查）
2. 规则配置向导
3. 规则测试功能
4. 执行日志查看
5. 规则模板管理
6. 批量导入导出

## 实施步骤

### 第一阶段：基础架构
1. 创建数据库表
2. 实现规则引擎核心类
3. 创建规则管理基础接口

### 第二阶段：规则迁移
1. 分析现有硬编码规则
2. 将规则转换为配置数据
3. 重构Service类调用规则引擎

### 第三阶段：管理界面
1. 开发后台管理界面
2. 实现规则配置功能
3. 添加测试和日志功能

### 第四阶段：优化完善
1. 性能优化和缓存
2. 错误处理和监控
3. 文档和培训

## 兼容性保证
1. 保持现有Service类接口不变
2. 支持新旧规则并存
3. 提供平滑迁移方案
4. 完整的回滚机制

## 预期收益
1. 大幅减少代码修改需求
2. 提高特殊需求配置效率
3. 降低维护成本
4. 提升系统灵活性
5. 减少发布频率
