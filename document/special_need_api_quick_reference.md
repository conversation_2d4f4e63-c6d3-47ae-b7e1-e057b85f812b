# 特殊需求配置系统 API 快速参考

## 🚀 核心接口

### 字段替换配置
```bash
# 获取列表
GET /admin/specialNeed/configIndex

# 创建配置
POST /admin/specialNeed/configCreate
{
  "SpecialNeedConfig": {
    "name": "配置名称",
    "type": "job|announcement|company", 
    "target_id": 123456,
    "field_name": "major|education|amount|applyType",
    "field_value": "新的字段值",
    "platform": "ALL|PC|H5|MINI"
  }
}

# 更新配置
POST /admin/specialNeed/configUpdate?id={id}

# 删除配置
POST /admin/specialNeed/configDelete?id={id}

# 批量状态操作
POST /admin/specialNeed/configBatchStatus
{
  "ids": [1,2,3],
  "status": 1
}
```

### 投递限制配置
```bash
# 获取列表
GET /admin/specialNeed/limitIndex

# 创建次数限制
POST /admin/specialNeed/limitCreate
{
  "SpecialNeedApplyLimit": {
    "name": "限制名称",
    "company_id": 89616,
    "limit_type": "count",
    "time_limit": 15,
    "count_limit": 1,
    "error_message": "错误提示"
  }
}

# 创建条件限制
POST /admin/specialNeed/limitCreate
{
  "SpecialNeedApplyLimit": {
    "name": "限制名称", 
    "company_id": 60,
    "limit_type": "condition",
    "condition_field": "is_abroad",
    "condition_value": "not_allowed",
    "error_message": "错误提示"
  }
}
```

## 📋 数据字典

### 配置类型
- `job` - 职位信息
- `announcement` - 公告信息  
- `company` - 单位信息

### 字段名称
- `major` - 专业要求
- `education` - 学历要求
- `amount` - 招聘人数
- `applyType` - 报名方式
- `jobCategory` - 职位类别

### 平台类型
- `ALL` - 全平台
- `PC` - PC端
- `H5` - H5端
- `MINI` - 小程序

### 限制类型
- `count` - 次数限制
- `condition` - 条件限制

## 🔧 工具接口

```bash
# 获取字段选项
GET /admin/specialNeed/getFieldOptions?type=job

# 测试配置
POST /admin/specialNeed/testConfig
{
  "type": "job",
  "target_id": 301480,
  "platform": "PC"
}

# 导出配置
GET /admin/specialNeed/exportConfig

# 导入配置
POST /admin/specialNeed/importConfig
```

## ✅ 表单验证

### 必填字段
**字段替换配置**:
- `name` (配置名称)
- `type` (配置类型) 
- `target_id` (目标ID)
- `field_name` (字段名称)
- `field_value` (字段值)
- `platform` (适用平台)

**投递限制配置**:
- `name` (限制名称)
- `company_id` (单位ID)
- `limit_type` (限制类型)
- `error_message` (错误提示)

### 条件验证
- 次数限制时: `time_limit` 和 `count_limit` 必填
- 条件限制时: `condition_field` 和 `condition_value` 必填

## 📝 使用示例

### 示例1: 修改职位专业要求
```javascript
{
  "SpecialNeedConfig": {
    "name": "电子科技大学专业修改",
    "type": "job",
    "target_id": 301480,
    "field_name": "major", 
    "field_value": "马克思主义理论、心理学或学校其他学科专业背景",
    "platform": "ALL"
  }
}
```

### 示例2: 设置投递次数限制
```javascript
{
  "SpecialNeedApplyLimit": {
    "name": "中科院金属所投递限制",
    "company_id": 89616,
    "limit_type": "count",
    "time_limit": 15,
    "count_limit": 1,
    "error_message": "您已超出该单位简历投递次数限制，无法再行投递"
  }
}
```

### 示例3: 设置条件限制
```javascript
{
  "SpecialNeedApplyLimit": {
    "name": "海外经历限制",
    "company_id": 60,
    "limit_type": "condition", 
    "condition_field": "is_abroad",
    "condition_value": "not_allowed",
    "error_message": "对不起，您暂不符合招聘要求，建议尝试其他机会！"
  }
}
```

## 🎯 前端组件建议

### 表单组件
```jsx
// 配置类型选择器
<Select value={type} onChange={setType}>
  <Option value="job">职位信息</Option>
  <Option value="announcement">公告信息</Option>
  <Option value="company">单位信息</Option>
</Select>

// 字段选择器 (动态加载)
<FieldSelector 
  configType={type}
  value={fieldName}
  onChange={setFieldName}
/>

// 平台选择器
<Checkbox.Group value={platform} onChange={setPlatform}>
  <Checkbox value="PC">PC端</Checkbox>
  <Checkbox value="H5">H5端</Checkbox>
  <Checkbox value="MINI">小程序</Checkbox>
</Checkbox.Group>

// 限制类型切换
<Radio.Group value={limitType} onChange={setLimitType}>
  <Radio value="count">次数限制</Radio>
  <Radio value="condition">条件限制</Radio>
</Radio.Group>
```

### 状态管理
```javascript
// Redux Actions
const actions = {
  fetchConfigs: (params) => ({ type: 'FETCH_CONFIGS', params }),
  createConfig: (data) => ({ type: 'CREATE_CONFIG', data }),
  updateConfig: (id, data) => ({ type: 'UPDATE_CONFIG', id, data }),
  deleteConfig: (id) => ({ type: 'DELETE_CONFIG', id }),
  batchUpdateStatus: (ids, status) => ({ type: 'BATCH_UPDATE_STATUS', ids, status })
};
```

## ⚠️ 注意事项

1. **ID准确性**: 确保target_id和company_id正确
2. **字段匹配**: 不同配置类型支持的字段不同
3. **平台兼容**: 注意不同平台的字段名称差异
4. **时间控制**: 支持设置生效时间范围
5. **优先级**: 新配置优先于硬编码逻辑
6. **测试验证**: 创建配置后建议先测试

## 🔗 相关链接

- [完整API文档](./special_need_api_docs.md)
- [使用指南](./special_need_usage_guide.md)
- [重构方案](./simple_special_need_plan.md)
