# 特殊需求简化配置方案

## 设计理念
让使用人员通过简单的表单配置特殊需求，无需了解复杂的规则语法。只需要：
1. 选择影响范围（单位ID/公告ID/职位ID）
2. 选择要修改的字段
3. 输入修改后的内容
4. 选择适用平台

## 数据库设计

### 特殊需求配置表 (special_need_config)
```sql
CREATE TABLE `special_need_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '配置名称',
  `type` varchar(20) NOT NULL DEFAULT '' COMMENT '类型：company,announcement,job,apply_limit',
  `target_id` int(11) NOT NULL DEFAULT 0 COMMENT '目标ID（单位ID/公告ID/职位ID）',
  `field_name` varchar(50) NOT NULL DEFAULT '' COMMENT '字段名称',
  `field_value` text COMMENT '字段值',
  `platform` varchar(20) NOT NULL DEFAULT 'ALL' COMMENT '适用平台：ALL,PC,H5,MINI',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `remark` text COMMENT '备注说明',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type_target` (`type`, `target_id`),
  KEY `idx_status_time` (`status`, `start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='特殊需求配置表';
```

### 投递限制配置表 (special_need_apply_limit)
```sql
CREATE TABLE `special_need_apply_limit` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '限制名称',
  `company_id` int(11) NOT NULL DEFAULT 0 COMMENT '单位ID',
  `limit_type` varchar(20) NOT NULL DEFAULT 'count' COMMENT '限制类型：count=次数限制,condition=条件限制',
  `time_limit` int(11) NOT NULL DEFAULT 0 COMMENT '时间限制（天）',
  `count_limit` int(11) NOT NULL DEFAULT 1 COMMENT '次数限制',
  `condition_field` varchar(50) NOT NULL DEFAULT '' COMMENT '条件字段（如is_abroad）',
  `condition_value` varchar(100) NOT NULL DEFAULT '' COMMENT '条件值',
  `error_message` varchar(255) NOT NULL DEFAULT '' COMMENT '错误提示信息',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `remark` text COMMENT '备注说明',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_status` (`company_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='投递限制配置表';
```

## 配置界面设计

### 1. 字段替换配置
- **配置类型**：下拉选择（单位信息/公告信息/职位信息）
- **目标ID**：输入框（支持单位ID/公告ID/职位ID）
- **字段名称**：下拉选择（预设常用字段）
- **字段值**：文本框（支持多行）
- **适用平台**：多选框（PC/H5/小程序）
- **生效时间**：日期时间选择器

### 2. 投递限制配置
- **单位ID**：输入框
- **限制类型**：单选（次数限制/条件限制）
- **时间范围**：输入框（天数）
- **次数限制**：输入框
- **条件限制**：下拉选择（海外经历等）
- **错误提示**：文本框

## 预设字段选项

### 职位信息字段
- education（学历要求）
- major（专业要求）
- amount（招聘人数）
- applyType（报名方式）
- jobCategory（职位类别）

### 公告信息字段
- recruitAmount（招聘人数）
- major（专业要求）
- education（学历要求）

### 单位信息字段
- recruitAmount（招聘人数）
- major（专业要求）

## 使用流程

### 添加字段替换配置
1. 点击"新增配置"
2. 选择配置类型（如"职位信息"）
3. 输入职位ID（如：1278773）
4. 选择字段（如"专业要求"）
5. 输入新值（如"计算机相关专业"）
6. 选择平台（如"全平台"）
7. 保存配置

### 添加投递限制
1. 点击"新增投递限制"
2. 输入单位ID（如：89616）
3. 选择限制类型（如"次数限制"）
4. 设置时间范围（如：15天）
5. 设置次数限制（如：1次）
6. 输入错误提示
7. 保存配置

## 技术实现

### 1. 配置加载
```php
// 根据类型和ID加载配置
$configs = SpecialNeedConfig::getConfigs($type, $targetId, $platform);
```

### 2. 字段替换
```php
// 应用字段替换
foreach ($configs as $config) {
    if ($config['field_name'] == 'education') {
        $data['education'] = $config['field_value'];
    }
}
```

### 3. 投递限制检查
```php
// 检查投递限制
$limits = SpecialNeedApplyLimit::getByCompanyId($companyId);
foreach ($limits as $limit) {
    // 执行限制检查
}
```

## 管理界面功能

### 配置列表页面
- 配置列表展示（分页）
- 搜索功能（按类型、目标ID）
- 批量启用/禁用
- 导入/导出功能

### 配置编辑页面
- 表单验证
- 实时预览
- 测试功能
- 历史记录

### 投递限制管理
- 限制规则列表
- 规则测试
- 执行日志查看

## 优势
1. **简单易用**：无需学习复杂规则语法
2. **直观明了**：所见即所得的配置方式
3. **快速配置**：几分钟即可完成一个特殊需求配置
4. **易于维护**：清晰的数据结构，便于后期维护
5. **向下兼容**：保持现有接口不变
