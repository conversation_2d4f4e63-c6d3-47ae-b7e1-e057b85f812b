# 特殊需求配置系统 API 接口文档

## 概述

本文档描述特殊需求配置系统的前端API接口，包括字段替换配置和投递限制配置的管理接口。

**基础URL**: `/admin/specialNeed/`

**认证方式**: 需要管理员登录权限

---

## 1. 字段替换配置管理

### 1.1 获取配置列表

**接口地址**: `GET /admin/specialNeed/configIndex`

**请求参数**:
```javascript
{
  "page": 1,                    // 页码，默认1
  "per-page": 20,              // 每页数量，默认20
  "SpecialNeedConfig[name]": "配置名称",     // 可选，模糊搜索
  "SpecialNeedConfig[type]": "job",         // 可选，类型筛选
  "SpecialNeedConfig[target_id]": 123456,   // 可选，目标ID
  "SpecialNeedConfig[status]": 1,           // 可选，状态筛选
  "SpecialNeedConfig[add_time]": "2024-01-01 - 2024-12-31" // 可选，时间范围
}
```

**响应示例**:
```javascript
{
  "msg": "",
  "result": 1,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "电子科技大学专业修改",
        "type": "job",
        "type_text": "职位信息",
        "target_id": 301480,
        "target_info": "职位ID: 301480",
        "field_name": "major",
        "field_text": "专业要求",
        "field_value": "马克思主义理论、心理学或学校其他学科专业背景",
        "platform": "ALL",
        "platform_text": "全平台",
        "status": 1,
        "status_text": "启用",
        "active_status_text": "生效中",
        "start_time": null,
        "end_time": null,
        "remark": "禅道需求1073",
        "created_by_name": "管理员",
        "add_time": "2024-01-01 10:00:00",
        "update_time": "2024-01-01 10:00:00"
      }
    ],
    "pages": {
      "total": 50,
      "limit": 20,
      "page": 1
    }
  }
}
```

### 1.2 获取配置详情

**接口地址**: `GET /admin/specialNeed/configView?id={id}`

**请求参数**:
- `id`: 配置ID

**响应示例**:
```javascript
{
  "msg": "",
  "result": 1,
  "data": {
    "id": 1,
    "name": "电子科技大学专业修改",
    "type": "job",
    "target_id": 301480,
    "field_name": "major",
    "field_value": "马克思主义理论、心理学或学校其他学科专业背景",
    "platform": "ALL",
    "status": 1,
    "start_time": null,
    "end_time": null,
    "remark": "禅道需求1073",
    "created_by": 1,
    "add_time": "2024-01-01 10:00:00",
    "update_time": "2024-01-01 10:00:00"
  }
}
```

### 1.3 创建配置

**接口地址**: `POST /admin/specialNeed/configCreate`

**请求参数**:
```javascript
{
  "SpecialNeedConfig": {
    "name": "配置名称",                    // 必填
    "type": "job",                        // 必填，job|announcement|company
    "target_id": 301480,                  // 必填，目标ID
    "field_name": "major",                // 必填，字段名称
    "field_value": "新的字段值",           // 必填，字段值
    "platform": "ALL",                   // 必填，ALL|PC|H5|MINI
    "status": 1,                         // 可选，默认1
    "start_time": "2024-01-01 00:00:00", // 可选，生效开始时间
    "end_time": "2024-12-31 23:59:59",   // 可选，生效结束时间
    "remark": "备注信息"                  // 可选
  }
}
```

**响应示例**:
```javascript
{
  "msg": "配置创建成功",
  "result": 1,
  "data": {
    "id": 123
  }
}
```

### 1.4 更新配置

**接口地址**: `POST /admin/specialNeed/configUpdate?id={id}`

**请求参数**: 同创建配置

**响应示例**:
```javascript
{
  "success": true,
  "message": "配置更新成功"
}
```

### 1.5 删除配置

**接口地址**: `POST /admin/specialNeed/configDelete?id={id}`

**响应示例**:
```javascript
{
  "success": true,
  "message": "配置删除成功"
}
```

### 1.6 批量状态操作

**接口地址**: `POST /admin/specialNeed/configBatchStatus`

**请求参数**:
```javascript
{
  "ids": [1, 2, 3],    // 配置ID数组
  "status": 1          // 状态：0=禁用，1=启用
}
```

**响应示例**:
```javascript
{
  "success": true,
  "message": "成功更新 3 条配置状态"
}
```

### 1.7 获取字段选项

**接口地址**: `GET /admin/specialNeed/getFieldOptions?type={type}`

**请求参数**:
- `type`: 配置类型 (job|announcement|company)

**响应示例**:
```javascript
{
  "education": "学历要求",
  "major": "专业要求",
  "majorTxt": "专业要求文本",
  "amount": "招聘人数",
  "applyType": "报名方式",
  "applyTypeText": "报名方式文本",
  "jobCategory": "职位类别"
}
```

### 1.8 测试配置

**接口地址**: `POST /admin/specialNeed/testConfig`

**请求参数**:
```javascript
{
  "type": "job",        // 配置类型
  "target_id": 301480,  // 目标ID
  "platform": "PC"     // 平台，默认PC
}
```

**响应示例**:
```javascript
{
  "success": true,
  "message": "找到 2 条匹配的配置",
  "data": [
    {
      "id": 1,
      "name": "电子科技大学专业修改",
      "field_name": "major",
      "field_value": "马克思主义理论、心理学或学校其他学科专业背景"
    }
  ]
}
```

---

## 2. 投递限制配置管理

### 2.1 获取限制列表

**接口地址**: `GET /admin/specialNeed/limitIndex`

**请求参数**:
```javascript
{
  "page": 1,
  "per-page": 20,
  "SpecialNeedApplyLimit[name]": "限制名称",
  "SpecialNeedApplyLimit[company_id]": 89616,
  "SpecialNeedApplyLimit[limit_type]": "count",
  "SpecialNeedApplyLimit[status]": 1
}
```

**响应示例**:
```javascript
{
  "msg": "",
  "result": 1,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "中科院金属所投递限制",
        "company_id": 89616,
        "company_info": "单位ID: 89616",
        "limit_type": "count",
        "limit_type_text": "次数限制",
        "limit_description": "15天内最多投递1次",
        "time_limit": 15,
        "count_limit": 1,
        "condition_field": null,
        "condition_value": null,
        "error_message": "您已超出该单位简历投递次数限制，无法再行投递",
        "status": 1,
        "status_text": "启用",
        "remark": "禅道需求775",
        "created_by_name": "管理员",
        "add_time": "2024-01-01 10:00:00"
      }
    ],
    "pagination": {
      "totalCount": 10,
      "pageCount": 1,
      "currentPage": 1,
      "perPage": 20
    }
  }
}
```

### 2.2 获取限制详情

**接口地址**: `GET /admin/specialNeed/limitView?id={id}`

**响应示例**:
```javascript
{
  "success": true,
  "data": {
    "id": 1,
    "name": "中科院金属所投递限制",
    "company_id": 89616,
    "limit_type": "count",
    "time_limit": 15,
    "count_limit": 1,
    "condition_field": null,
    "condition_value": null,
    "error_message": "您已超出该单位简历投递次数限制，无法再行投递",
    "status": 1,
    "remark": "禅道需求775",
    "created_by": 1,
    "add_time": "2024-01-01 10:00:00"
  }
}
```

### 2.3 创建投递限制

**接口地址**: `POST /admin/specialNeed/limitCreate`

**请求参数**:
```javascript
{
  "SpecialNeedApplyLimit": {
    "name": "限制名称",                    // 必填
    "company_id": 89616,                  // 必填，单位ID
    "limit_type": "count",                // 必填，count|condition
    "time_limit": 15,                     // 次数限制时必填，天数
    "count_limit": 1,                     // 次数限制时必填，次数
    "condition_field": "is_abroad",       // 条件限制时必填
    "condition_value": "not_allowed",     // 条件限制时必填
    "error_message": "错误提示信息",       // 必填
    "status": 1,                         // 可选，默认1
    "remark": "备注信息"                  // 可选
  }
}
```

**响应示例**:
```javascript
{
  "success": true,
  "message": "投递限制创建成功",
  "data": {
    "id": 123
  }
}
```

### 2.4 更新投递限制

**接口地址**: `POST /admin/specialNeed/limitUpdate?id={id}`

**请求参数**: 同创建投递限制

### 2.5 删除投递限制

**接口地址**: `POST /admin/specialNeed/limitDelete?id={id}`

---

## 3. 配置导入导出

### 3.1 导出配置

**接口地址**: `GET /admin/specialNeed/exportConfig`

**响应**: 直接下载JSON文件

### 3.2 导入配置

**接口地址**: `POST /admin/specialNeed/importConfig`

**请求参数**: 
- `config_file`: 上传的JSON文件

**响应示例**:
```javascript
{
  "success": true,
  "message": "成功导入 5 条配置"
}
```

---

## 4. 数据字典

### 4.1 配置类型 (type)
```javascript
{
  "job": "职位信息",
  "announcement": "公告信息", 
  "company": "单位信息"
}
```

### 4.2 平台类型 (platform)
```javascript
{
  "ALL": "全平台",
  "PC": "PC端",
  "H5": "H5端",
  "MINI": "小程序"
}
```

### 4.3 状态 (status)
```javascript
{
  "0": "禁用",
  "1": "启用"
}
```

### 4.4 限制类型 (limit_type)
```javascript
{
  "count": "次数限制",
  "condition": "条件限制"
}
```

### 4.5 条件字段 (condition_field)
```javascript
{
  "is_abroad": "海外经历",
  "education_level": "学历水平",
  "work_experience": "工作经验"
}
```

---

## 5. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

---

## 6. 使用示例

### 6.1 创建职位专业修改配置

```javascript
// 请求
POST /admin/special-need/config-create
{
  "SpecialNeedConfig": {
    "name": "电子科技大学专业修改",
    "type": "job",
    "target_id": 301480,
    "field_name": "major",
    "field_value": "马克思主义理论、心理学或学校其他学科专业背景",
    "platform": "ALL",
    "remark": "禅道需求1073"
  }
}

// 响应
{
  "success": true,
  "message": "配置创建成功",
  "data": {"id": 123}
}
```

### 6.2 创建投递次数限制

```javascript
// 请求
POST /admin/special-need/limit-create
{
  "SpecialNeedApplyLimit": {
    "name": "中科院金属所投递限制",
    "company_id": 89616,
    "limit_type": "count",
    "time_limit": 15,
    "count_limit": 1,
    "error_message": "您已超出该单位简历投递次数限制，无法再行投递",
    "remark": "禅道需求775"
  }
}

// 响应
{
  "success": true,
  "message": "投递限制创建成功",
  "data": {"id": 456}
}
```

---

## 7. 表单验证规则

### 7.1 字段替换配置验证

```javascript
{
  "name": {
    "required": true,
    "maxLength": 100,
    "message": "配置名称不能为空且不超过100个字符"
  },
  "type": {
    "required": true,
    "enum": ["job", "announcement", "company"],
    "message": "请选择正确的配置类型"
  },
  "target_id": {
    "required": true,
    "type": "integer",
    "min": 1,
    "message": "目标ID必须是大于0的整数"
  },
  "field_name": {
    "required": true,
    "maxLength": 50,
    "message": "字段名称不能为空且不超过50个字符"
  },
  "field_value": {
    "required": true,
    "maxLength": 5000,
    "message": "字段值不能为空且不超过5000个字符"
  },
  "platform": {
    "required": true,
    "enum": ["ALL", "PC", "H5", "MINI"],
    "message": "请选择正确的适用平台"
  },
  "start_time": {
    "type": "datetime",
    "message": "请选择正确的开始时间格式"
  },
  "end_time": {
    "type": "datetime",
    "message": "请选择正确的结束时间格式",
    "validate": "end_time > start_time"
  }
}
```

### 7.2 投递限制配置验证

```javascript
{
  "name": {
    "required": true,
    "maxLength": 100,
    "message": "限制名称不能为空且不超过100个字符"
  },
  "company_id": {
    "required": true,
    "type": "integer",
    "min": 1,
    "message": "单位ID必须是大于0的整数"
  },
  "limit_type": {
    "required": true,
    "enum": ["count", "condition"],
    "message": "请选择正确的限制类型"
  },
  "time_limit": {
    "requiredIf": "limit_type === 'count'",
    "type": "integer",
    "min": 0,
    "message": "时间限制必须是大于等于0的整数"
  },
  "count_limit": {
    "requiredIf": "limit_type === 'count'",
    "type": "integer",
    "min": 1,
    "message": "次数限制必须是大于0的整数"
  },
  "condition_field": {
    "requiredIf": "limit_type === 'condition'",
    "enum": ["is_abroad", "education_level", "work_experience"],
    "message": "请选择正确的条件字段"
  },
  "condition_value": {
    "requiredIf": "limit_type === 'condition'",
    "maxLength": 100,
    "message": "条件值不能为空且不超过100个字符"
  },
  "error_message": {
    "required": true,
    "maxLength": 255,
    "message": "错误提示信息不能为空且不超过255个字符"
  }
}
```

---

## 8. 前端组件建议

### 8.1 配置表单组件

```javascript
// 字段选择组件 - 根据配置类型动态加载字段选项
<FieldSelector
  configType={formData.type}
  value={formData.field_name}
  onChange={handleFieldChange}
  api="/admin/special-need/get-field-options"
/>

// 平台选择组件
<PlatformSelector
  value={formData.platform}
  onChange={handlePlatformChange}
  options={["ALL", "PC", "H5", "MINI"]}
/>

// 时间范围选择组件
<DateTimeRangePicker
  startTime={formData.start_time}
  endTime={formData.end_time}
  onChange={handleTimeRangeChange}
/>
```

### 8.2 限制类型切换组件

```javascript
// 限制类型选择器 - 根据类型显示不同的表单字段
<LimitTypeSelector
  value={formData.limit_type}
  onChange={handleLimitTypeChange}
>
  {formData.limit_type === 'count' && (
    <CountLimitFields
      timeLimit={formData.time_limit}
      countLimit={formData.count_limit}
      onChange={handleCountFieldsChange}
    />
  )}

  {formData.limit_type === 'condition' && (
    <ConditionLimitFields
      conditionField={formData.condition_field}
      conditionValue={formData.condition_value}
      onChange={handleConditionFieldsChange}
    />
  )}
</LimitTypeSelector>
```

### 8.3 配置测试组件

```javascript
// 配置测试工具
<ConfigTester
  onTest={handleConfigTest}
  api="/admin/special-need/test-config"
>
  <input placeholder="配置类型" name="type" />
  <input placeholder="目标ID" name="target_id" />
  <input placeholder="平台" name="platform" />
  <button type="submit">测试配置</button>
</ConfigTester>
```

---

## 9. 状态管理建议

### 9.1 Redux Store 结构

```javascript
{
  specialNeed: {
    configs: {
      list: [],
      pagination: {},
      loading: false,
      filters: {}
    },
    limits: {
      list: [],
      pagination: {},
      loading: false,
      filters: {}
    },
    options: {
      fieldOptions: {},
      typeOptions: {},
      platformOptions: {},
      statusOptions: {}
    }
  }
}
```

### 9.2 API 调用示例

```javascript
// 获取配置列表
const fetchConfigs = async (params) => {
  try {
    const response = await api.get('/admin/special-need/config-index', { params });
    return response.data;
  } catch (error) {
    console.error('获取配置列表失败:', error);
    throw error;
  }
};

// 创建配置
const createConfig = async (configData) => {
  try {
    const response = await api.post('/admin/special-need/config-create', {
      SpecialNeedConfig: configData
    });
    return response.data;
  } catch (error) {
    console.error('创建配置失败:', error);
    throw error;
  }
};

// 批量操作
const batchUpdateStatus = async (ids, status) => {
  try {
    const response = await api.post('/admin/special-need/config-batch-status', {
      ids,
      status
    });
    return response.data;
  } catch (error) {
    console.error('批量更新状态失败:', error);
    throw error;
  }
};
```

---

## 10. 页面路由建议

```javascript
// 路由配置
const routes = [
  {
    path: '/special-need',
    component: SpecialNeedLayout,
    children: [
      {
        path: 'config',
        component: ConfigManagement,
        children: [
          { path: '', component: ConfigList },
          { path: 'create', component: ConfigCreate },
          { path: 'edit/:id', component: ConfigEdit },
          { path: 'view/:id', component: ConfigView }
        ]
      },
      {
        path: 'limit',
        component: LimitManagement,
        children: [
          { path: '', component: LimitList },
          { path: 'create', component: LimitCreate },
          { path: 'edit/:id', component: LimitEdit },
          { path: 'view/:id', component: LimitView }
        ]
      }
    ]
  }
];
```
