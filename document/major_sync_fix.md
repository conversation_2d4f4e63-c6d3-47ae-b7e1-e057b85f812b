# Major字段同步问题修复说明

## 问题描述

在不同端的数据结构中，`major` 字段的格式不同，导致同步问题：

### H5端数据结构
```php
$h5Data = [
    'majorTxt' => '临床医学',  // 文本字段
    'major' => [              // 数组字段
        [
            'majorText' => '旧的专业文本',
            'url' => '#'
        ]
    ]
];
```

### 小程序端数据结构
```php
$miniData = [
    'majorTxt' => '临床医学',           // 文本字段
    'major' => '原始专业字符串',        // 字符串字段（不是数组）
];
```

**问题现象**：
- 配置应用后，只有一个字段被更新，另一个字段还是显示旧的内容
- 例如：`majorTxt` 显示"临床医学"，`major` 显示"我是需求专业新文本"
- 用户期望两个字段都显示相同的新值

## 问题原因

1. **字段转换优先级问题**：原来的逻辑优先选择 `major` 字段，但应该优先选择 `majorTxt` 字段
2. **同步逻辑不完整**：只更新目标字段，没有同步更新所有相关字段
3. **数据格式差异**：没有考虑到 `major` 字段在不同端的格式差异（数组 vs 字符串）
4. **用户期望不匹配**：用户期望所有相关字段都显示相同的新值

## 解决方案

### 1. 优化字段转换逻辑

修改 `convertFieldName` 方法，优先选择文本字段：

```php
// 修改前：优先选择major字段
if (isset($data['major'])) {
    return 'major';
} elseif (isset($data['majorTxt'])) {
    return 'majorTxt';
}

// 修改后：优先选择majorTxt字段
if (isset($data['majorTxt'])) {
    return 'majorTxt';
} elseif (isset($data['major'])) {
    return 'major';
}
```

### 2. 完善同步逻辑

在 `applyConfigs` 方法中添加完整的同步逻辑，确保所有相关字段都被更新：

```php
case 'major':
    // 先更新目标字段
    $data[$actualFieldName] = $fieldValue;
    $finalValue = $fieldValue;

    // 无论更新哪个字段，都要同步更新所有相关字段，确保数据一致性

    // 更新majorTxt字段
    if (isset($data['majorTxt'])) {
        $data['majorTxt'] = $finalValue;
    }

    // 更新major字段
    if (isset($data['major'])) {
        if (is_array($data['major'])) {
            // H5端：major是数组格式
            $data['major'] = [
                [
                    'majorText' => $finalValue,
                    'url'       => '#',
                ],
            ];
        } else {
            // 小程序端：major是字符串格式
            $data['major'] = $finalValue;
        }
    }
    break;
```

## 修复效果

### 修复前

#### H5端问题
```php
// 配置应用前
$h5Data = [
    'majorTxt' => '临床医学',
    'major' => [
        ['majorText' => '旧的专业文本', 'url' => '#']
    ]
];

// 应用配置：major = '我是需求专业新文本'
// 结果：
$h5Data = [
    'majorTxt' => '我是需求专业新文本',  // ✅ 正确更新
    'major' => [
        ['majorText' => '旧的专业文本', 'url' => '#']  // ❌ 没有同步
    ]
];
```

#### 小程序端问题
```php
// 配置应用前
$miniData = [
    'majorTxt' => '临床医学',
    'major' => '原始专业字符串'
];

// 应用配置：major = '我是需求专业新文本'
// 结果：
$miniData = [
    'majorTxt' => '临床医学',           // ❌ 没有同步
    'major' => '我是需求专业新文本'     // ✅ 正确更新
];
```

### 修复后

#### H5端修复
```php
// 配置应用前
$h5Data = [
    'majorTxt' => '临床医学',
    'major' => [
        ['majorText' => '旧的专业文本', 'url' => '#']
    ]
];

// 应用配置：major = '我是需求专业新文本'
// 结果：
$h5Data = [
    'majorTxt' => '我是需求专业新文本',  // ✅ 正确更新
    'major' => [
        ['majorText' => '我是需求专业新文本', 'url' => '#']  // ✅ 同步更新
    ]
];
```

#### 小程序端修复
```php
// 配置应用前
$miniData = [
    'majorTxt' => '临床医学',
    'major' => '原始专业字符串'
];

// 应用配置：major = '我是需求专业新文本'
// 结果：
$miniData = [
    'majorTxt' => '我是需求专业新文本',  // ✅ 正确更新
    'major' => '我是需求专业新文本'     // ✅ 同步更新
];
```

## 适用场景

这个修复适用于以下场景：

1. **H5端数据结构**：同时有 `major` 数组和 `majorTxt` 文本字段
2. **小程序端数据结构**：同时有 `major` 字符串和 `majorTxt` 文本字段
3. **PC端数据结构**：只有 `major` 字段（ID格式）
4. **公告数据结构**：只有 `majorTxt` 文本字段

系统会根据实际的数据结构智能选择合适的字段进行更新，并保持相关字段的同步。

## 扩展性

同样的逻辑也适用于 `applyType` 和 `applyTypeText` 字段的同步，确保报名方式字段的一致性。

## 测试验证

可以运行以下测试文件来验证修复效果：

### H5端测试
```bash
php test_major_sync.php
```

### 小程序端测试
```bash
php test_mini_program_sync.php
```

### 完整同步测试
```bash
php test_complete_sync.php
```

测试会验证：
1. 字段转换逻辑是否正确
2. 所有相关字段是否完全同步
3. 不同数据结构的兼容性（H5数组格式 vs 小程序字符串格式）
4. 追加功能是否正常
5. 报名方式字段同步是否正常

## 总结

通过这个修复：

1. ✅ 解决了H5端 `major` 数组字段不同步的问题
2. ✅ 解决了小程序端 `major` 字符串字段不同步的问题
3. ✅ 优化了字段转换的优先级逻辑
4. ✅ 保持了向后兼容性
5. ✅ 支持不同端的数据结构差异（数组 vs 字符串）
6. ✅ 确保了数据的一致性

现在当用户配置专业要求时，无论是H5端、小程序端还是PC端，所有相关字段都会被正确同步更新。
