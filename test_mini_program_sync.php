<?php
/**
 * 测试小程序端major字段同步功能
 * 验证当major配置应用时，majorTxt字段是否会被正确同步
 */

require_once __DIR__ . '/common/base/models/BaseSpecialNeedConfig.php';

echo "=== 测试小程序端major字段同步功能 ===\n\n";

// 模拟小程序端的数据结构
$miniData = [
    'id' => 123,
    'title' => '软件工程师',
    'majorTxt' => '临床医学',           // 文本字段
    'major' => '原始专业字符串',        // 字符串字段（不是数组）
    'education' => '本科'
];

echo "原始小程序数据:\n";
print_r($miniData);

// 模拟配置数据
$configs = [
    [
        'field_name' => 'major',
        'field_value' => '我是需求专业新文本',
        'type' => 'job'
    ]
];

echo "配置数据:\n";
print_r($configs);

// 测试字段转换逻辑
echo "\n=== 测试字段转换逻辑 ===\n";
$actualField = \common\base\models\BaseSpecialNeedConfig::convertFieldName('major', $miniData, 'job');
echo "major字段在小程序数据中转换为: {$actualField}\n";
echo "说明: 小程序数据同时有major(字符串)和majorTxt字段，优先选择majorTxt字段进行更新\n";

// 应用配置
echo "\n=== 应用配置 ===\n";
$modifiedData = \common\base\models\BaseSpecialNeedConfig::applyConfigs($miniData, $configs, 'job');

echo "应用配置后的数据:\n";
print_r($modifiedData);

// 验证结果
echo "\n=== 验证结果 ===\n";

$expectedValue = '我是需求专业新文本';

if (isset($modifiedData['majorTxt']) && $modifiedData['majorTxt'] === $expectedValue) {
    echo "✅ majorTxt字段更新正确: {$modifiedData['majorTxt']}\n";
} else {
    echo "❌ majorTxt字段更新失败\n";
    if (isset($modifiedData['majorTxt'])) {
        echo "   期望值: {$expectedValue}\n";
        echo "   实际值: {$modifiedData['majorTxt']}\n";
    }
}

if (isset($modifiedData['major']) && $modifiedData['major'] === $expectedValue) {
    echo "✅ major字段同步正确: {$modifiedData['major']}\n";
} else {
    echo "❌ major字段同步失败\n";
    if (isset($modifiedData['major'])) {
        echo "   期望值: {$expectedValue}\n";
        echo "   实际值: {$modifiedData['major']}\n";
    }
}

// 检查是否两个字段都是相同的值
if (isset($modifiedData['majorTxt']) && isset($modifiedData['major']) &&
    $modifiedData['majorTxt'] === $modifiedData['major'] &&
    $modifiedData['majorTxt'] === $expectedValue) {
    echo "✅ 两个字段完全同步，值都是: {$expectedValue}\n";
} else {
    echo "❌ 两个字段没有完全同步\n";
}

// 测试H5端数据结构（对比）
echo "\n=== 对比测试H5端数据结构 ===\n";
$h5Data = [
    'id' => 456,
    'title' => '高级工程师',
    'majorTxt' => '临床医学',
    'major' => [              // 数组字段
        [
            'majorText' => '旧的专业文本',
            'url' => '#'
        ]
    ],
    'education' => '硕士'
];

echo "原始H5数据:\n";
print_r($h5Data);

$modifiedH5Data = \common\base\models\BaseSpecialNeedConfig::applyConfigs($h5Data, $configs, 'job');

echo "\n应用配置后的H5数据:\n";
print_r($modifiedH5Data);

// 验证H5端结果
echo "\n=== 验证H5端结果 ===\n";

if (isset($modifiedH5Data['majorTxt']) && $modifiedH5Data['majorTxt'] === '我是需求专业新文本') {
    echo "✅ H5端majorTxt字段更新正确: {$modifiedH5Data['majorTxt']}\n";
} else {
    echo "❌ H5端majorTxt字段更新失败\n";
}

if (isset($modifiedH5Data['major']) && is_array($modifiedH5Data['major']) && 
    isset($modifiedH5Data['major'][0]['majorText']) && 
    $modifiedH5Data['major'][0]['majorText'] === '我是需求专业新文本') {
    echo "✅ H5端major数组字段同步正确: {$modifiedH5Data['major'][0]['majorText']}\n";
} else {
    echo "❌ H5端major数组字段同步失败\n";
}

// 测试PC端数据结构（只有major字段）
echo "\n=== 测试PC端数据结构 ===\n";
$pcData = [
    'id' => 789,
    'title' => '资深工程师',
    'major' => '120,121',  // 只有major字段，ID格式
    'education' => '博士'
];

echo "原始PC数据:\n";
print_r($pcData);

$actualFieldPc = \common\base\models\BaseSpecialNeedConfig::convertFieldName('major', $pcData, 'job');
echo "major字段在PC数据中转换为: {$actualFieldPc}\n";

$modifiedPcData = \common\base\models\BaseSpecialNeedConfig::applyConfigs($pcData, $configs, 'job');

echo "\n应用配置后的PC数据:\n";
print_r($modifiedPcData);

if (isset($modifiedPcData['major']) && $modifiedPcData['major'] === '我是需求专业新文本') {
    echo "✅ PC端major字段更新正确: {$modifiedPcData['major']}\n";
} else {
    echo "❌ PC端major字段更新失败\n";
}

echo "\n=== 总结 ===\n";
echo "1. 小程序端: majorTxt和major(字符串)字段都会被正确同步\n";
echo "2. H5端: majorTxt和major(数组)字段都会被正确同步\n";
echo "3. PC端: 只有major字段时，直接更新major字段\n";
echo "4. 系统会根据数据结构智能处理不同格式的major字段\n";

echo "\n=== 测试完成 ===\n";
