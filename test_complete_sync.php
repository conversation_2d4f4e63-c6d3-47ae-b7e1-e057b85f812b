<?php
/**
 * 测试完整同步功能
 * 验证当配置应用时，所有相关字段都会被同步更新
 */

require_once __DIR__ . '/common/base/models/BaseSpecialNeedConfig.php';

echo "=== 测试完整同步功能 ===\n\n";

// 测试场景1：小程序端数据结构
echo "1. 测试小程序端数据结构:\n";
$miniData = [
    'id' => 123,
    'title' => '软件工程师',
    'majorTxt' => '临床医学',           // 原始值
    'major' => '原始专业字符串',        // 原始值
    'education' => '本科'
];

echo "原始数据:\n";
foreach ($miniData as $key => $value) {
    if (in_array($key, ['majorTxt', 'major'])) {
        echo "  {$key}: \"{$value}\"\n";
    }
}

// 应用配置
$configs = [
    [
        'field_name' => 'major',
        'field_value' => '我是需求专业新文本',
        'type' => 'job'
    ]
];

$result = \common\base\models\BaseSpecialNeedConfig::applyConfigs($miniData, $configs, 'job');

echo "\n应用配置后:\n";
foreach ($result as $key => $value) {
    if (in_array($key, ['majorTxt', 'major'])) {
        echo "  {$key}: \"{$value}\"\n";
    }
}

// 验证结果
$expectedValue = '我是需求专业新文本';
$majorTxtCorrect = isset($result['majorTxt']) && $result['majorTxt'] === $expectedValue;
$majorCorrect = isset($result['major']) && $result['major'] === $expectedValue;

echo "\n验证结果:\n";
echo "  majorTxt字段: " . ($majorTxtCorrect ? "✅ 正确" : "❌ 错误") . "\n";
echo "  major字段: " . ($majorCorrect ? "✅ 正确" : "❌ 错误") . "\n";
echo "  完全同步: " . ($majorTxtCorrect && $majorCorrect ? "✅ 成功" : "❌ 失败") . "\n";

// 测试场景2：H5端数据结构
echo "\n\n2. 测试H5端数据结构:\n";
$h5Data = [
    'id' => 456,
    'title' => '高级工程师',
    'majorTxt' => '临床医学',
    'major' => [
        [
            'majorText' => '旧的专业文本',
            'url' => '#'
        ]
    ],
    'education' => '硕士'
];

echo "原始数据:\n";
echo "  majorTxt: \"{$h5Data['majorTxt']}\"\n";
echo "  major[0]['majorText']: \"{$h5Data['major'][0]['majorText']}\"\n";

$h5Result = \common\base\models\BaseSpecialNeedConfig::applyConfigs($h5Data, $configs, 'job');

echo "\n应用配置后:\n";
echo "  majorTxt: \"{$h5Result['majorTxt']}\"\n";
echo "  major[0]['majorText']: \"{$h5Result['major'][0]['majorText']}\"\n";

// 验证H5结果
$h5MajorTxtCorrect = isset($h5Result['majorTxt']) && $h5Result['majorTxt'] === $expectedValue;
$h5MajorCorrect = isset($h5Result['major'][0]['majorText']) && $h5Result['major'][0]['majorText'] === $expectedValue;

echo "\n验证结果:\n";
echo "  majorTxt字段: " . ($h5MajorTxtCorrect ? "✅ 正确" : "❌ 错误") . "\n";
echo "  major数组字段: " . ($h5MajorCorrect ? "✅ 正确" : "❌ 错误") . "\n";
echo "  完全同步: " . ($h5MajorTxtCorrect && $h5MajorCorrect ? "✅ 成功" : "❌ 失败") . "\n";

// 测试场景3：报名方式字段同步
echo "\n\n3. 测试报名方式字段同步:\n";
$applyData = [
    'id' => 789,
    'title' => '测试职位',
    'applyType' => '1,2',
    'applyTypeText' => '电子邮件,现场报名',
];

echo "原始数据:\n";
echo "  applyType: \"{$applyData['applyType']}\"\n";
echo "  applyTypeText: \"{$applyData['applyTypeText']}\"\n";

$applyConfigs = [
    [
        'field_name' => 'applyType',
        'field_value' => '网上报名,邮件报名',
        'type' => 'job'
    ]
];

$applyResult = \common\base\models\BaseSpecialNeedConfig::applyConfigs($applyData, $applyConfigs, 'job');

echo "\n应用配置后:\n";
echo "  applyType: \"{$applyResult['applyType']}\"\n";
echo "  applyTypeText: \"{$applyResult['applyTypeText']}\"\n";

// 验证报名方式结果
$expectedApplyValue = '网上报名,邮件报名';
$applyTypeCorrect = isset($applyResult['applyType']) && $applyResult['applyType'] === $expectedApplyValue;
$applyTypeTextCorrect = isset($applyResult['applyTypeText']) && $applyResult['applyTypeText'] === $expectedApplyValue;

echo "\n验证结果:\n";
echo "  applyType字段: " . ($applyTypeCorrect ? "✅ 正确" : "❌ 错误") . "\n";
echo "  applyTypeText字段: " . ($applyTypeTextCorrect ? "✅ 正确" : "❌ 错误") . "\n";
echo "  完全同步: " . ($applyTypeCorrect && $applyTypeTextCorrect ? "✅ 成功" : "❌ 失败") . "\n";

// 总结
echo "\n\n=== 总结 ===\n";
$allTestsPassed = $majorTxtCorrect && $majorCorrect && $h5MajorTxtCorrect && $h5MajorCorrect && $applyTypeCorrect && $applyTypeTextCorrect;

if ($allTestsPassed) {
    echo "🎉 所有测试通过！字段同步功能工作正常。\n";
    echo "\n功能特点:\n";
    echo "✅ 小程序端：majorTxt和major(字符串)完全同步\n";
    echo "✅ H5端：majorTxt和major(数组)完全同步\n";
    echo "✅ 报名方式：applyType和applyTypeText完全同步\n";
    echo "✅ 无论选择哪个字段作为目标，所有相关字段都会被更新\n";
} else {
    echo "❌ 部分测试失败，需要进一步检查。\n";
}

echo "\n=== 测试完成 ===\n";
