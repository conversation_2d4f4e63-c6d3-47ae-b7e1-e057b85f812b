<?php
/**
 * 特殊需求字段映射功能使用示例
 */

// 引入必要的类文件
require_once 'common/base/models/BaseSpecialNeedConfig.php';

echo "=== 特殊需求字段映射功能使用示例 ===\n\n";

// 示例1: 前端获取字段选项（统一后，无重复）
echo "1. 前端获取字段选项:\n";
echo "职位配置可选字段:\n";
$jobFields = \common\base\models\BaseSpecialNeedConfig::getFieldOptions('job');
foreach ($jobFields as $key => $label) {
    echo "  - {$label} (字段名: {$key})\n";
}

echo "\n公告配置可选字段:\n";
$announcementFields = \common\base\models\BaseSpecialNeedConfig::getFieldOptions('announcement');
foreach ($announcementFields as $key => $label) {
    echo "  - {$label} (字段名: {$key})\n";
}

// 示例2: 配置应用场景
echo "\n\n2. 配置应用场景:\n";

// 模拟用户在前端选择了"招聘人数"字段，并设置值为"若干"
$userConfig = [
    'field_name' => 'recruitAmount',  // 用户选择的统一字段名
    'field_value' => '若干',
    'type' => 'job'
];

echo "用户配置: 将职位的招聘人数设置为 '{$userConfig['field_value']}'\n";

// 模拟职位数据（使用amount字段）
$jobData = [
    'id' => 12345,
    'title' => '高级软件工程师',
    'amount' => 5,  // 原始数据使用amount字段
    'education' => '本科',
    'major' => '计算机科学'
];

echo "\n原始职位数据:\n";
print_r($jobData);

// 应用配置
$configs = [$userConfig];
$modifiedJobData = \common\base\models\BaseSpecialNeedConfig::applyConfigs($jobData, $configs, 'job');

echo "\n应用配置后的职位数据:\n";
print_r($modifiedJobData);

echo "注意: 系统自动将统一字段名 'recruitAmount' 转换为职位数据中实际使用的 'amount' 字段\n";

// 示例3: 公告数据的处理
echo "\n\n3. 公告数据处理示例:\n";

// 模拟公告数据（使用recruitAmount字段）
$announcementData = [
    'id' => 67890,
    'title' => '2024年春季招聘公告',
    'recruitAmount' => 20,  // 原始数据使用recruitAmount字段
    'education' => '硕士',
    'major' => '软件工程'
];

echo "原始公告数据:\n";
print_r($announcementData);

// 同样的配置应用到公告数据
$userConfig['type'] = 'announcement';
$configs = [$userConfig];
$modifiedAnnouncementData = \common\base\models\BaseSpecialNeedConfig::applyConfigs($announcementData, $configs, 'announcement');

echo "\n应用配置后的公告数据:\n";
print_r($modifiedAnnouncementData);

echo "注意: 系统自动将统一字段名 'recruitAmount' 转换为公告数据中实际使用的 'recruitAmount' 字段\n";

// 示例4: 字段显示名称获取
echo "\n\n4. 字段显示名称获取示例:\n";

$testFields = ['recruitAmount', 'amount', 'major', 'education'];
foreach ($testFields as $field) {
    $displayName = \common\base\models\BaseSpecialNeedConfig::getFieldDisplayName($field, 'job');
    echo "字段 '{$field}' 显示为: {$displayName}\n";
}

// 示例5: 向后兼容性演示
echo "\n\n5. 向后兼容性演示:\n";

// 模拟旧的配置数据（直接使用原始字段名）
$oldConfig = [
    'field_name' => 'amount',  // 旧配置直接使用原始字段名
    'field_value' => '3',
    'type' => 'job'
];

echo "旧配置数据: 直接使用 'amount' 字段名\n";
$oldConfigs = [$oldConfig];
$resultWithOldConfig = \common\base\models\BaseSpecialNeedConfig::applyConfigs($jobData, $oldConfigs, 'job');

echo "\n使用旧配置的结果:\n";
print_r($resultWithOldConfig);

echo "\n=== 总结 ===\n";
echo "1. 前端用户只看到统一的字段选项，不会有重复的'招聘人数'选项\n";
echo "2. 系统自动根据数据结构和配置类型转换字段名\n";
echo "3. 保持了向后兼容性，旧的配置数据仍然可以正常工作\n";
echo "4. 新增的字段映射可以轻松扩展到其他类似的字段\n";

echo "\n=== 示例完成 ===\n";
