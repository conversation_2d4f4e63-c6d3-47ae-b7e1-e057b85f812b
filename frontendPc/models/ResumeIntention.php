<?php

namespace frontendPc\models;

use common\base\models\BaseArea;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResumeIntention;
use common\libs\Cache;
use yii\base\Exception;

class ResumeIntention extends BaseResumeIntention
{

    /**
     * 获取单条求职意向
     * @param $memberId
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getInfo($memberId)
    {
        $info           = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'nature_type as natureType',
                'job_category_id as jobCategoryId',
                'area_id as areaId',
                'wage_type as wageType',
            ])
            ->asArray()
            ->one();
        $info['areaId'] = array_filter(explode(',', $info['areaId']));

        return $info;
    }

    /**
     * 获取多条求职意向
     * @param $memberId
     * @return array
     * @throws \Exception
     */
    public static function getInfoList($memberId): array
    {
        $list = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'nature_type as natureType',
                'job_category_id as jobCategoryId',
                'area_id as areaId',
                'wage_type as wageType',
            ])
            //            ->limit(self::LIMIT_NUM)
            ->asArray()
            ->all();

        $areaCache = BaseArea::setAreaCache();

        foreach ($list as $k => $v) {
            $list[$k]['jobCategoryName'] = CategoryJob::getName($v['jobCategoryId']);
            $list[$k]['wageName']        = Dictionary::getWageRangeName($v['wageType']);
            $list[$k]['natureName']      = Dictionary::getNatureName($v['natureType']);
            $list[$k]['areaId']          = explode(',', $list[$k]['areaId']);

            $areaNum = count($list[$k]['areaId']);

            $list[$k]['areaName'] = '';

            foreach ($list[$k]['areaId'] as $key => $val) {
                if ($key < $areaNum - 1) {
                    $list[$k]['areaName'] .= $areaCache[$val]['name'] . '/';
                } else {
                    $list[$k]['areaName'] .= $areaCache[$val]['name'];
                }
            }
        }

        return $list;
    }

    /**
     * 删除求职意向
     * @param $id
     * @throws Exception
     */
    public static function delIntention($id, $memberId)
    {
        // 先判断是否最后一条了
        $count = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->count();

        if ($count <= 1) {
            throw new Exception('至少保留一条求职意向');
        }

        $model = self::findOne($id);

        if ($model->member_id != $memberId) {
            throw new Exception('该记录与当前用户信息不匹配');
        }

        $model->status = self::STATUS_DELETE;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);

        //新增操作日志
        $data = [
            'content' => '删除求职意向id：' . $id,
        ];
        // 写登录日志
        BaseMemberActionLog::log($data);
    }
}