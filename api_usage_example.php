<?php
/**
 * API接口使用示例
 * 演示如何使用特殊需求配置的API接口
 */

echo "=== 特殊需求配置API使用示例 ===\n\n";

// 模拟API调用函数
function mockApiCall($method, $url, $data = null) {
    echo "API调用: {$method} {$url}\n";
    if ($data) {
        echo "请求数据: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    // 模拟不同接口的响应
    switch ($url) {
        case '/admin/specialNeed/getFieldOptions?type=job':
            return [
                'result' => 1,
                'msg' => '',
                'data' => [
                    'education' => '学历要求',
                    'major' => '专业要求',
                    'recruitAmount' => '招聘人数',
                    'applyType' => '报名方式',
                    'jobCategory' => '职位类别'
                ]
            ];
            
        case '/admin/specialNeed/getFieldOptions?type=announcement':
            return [
                'result' => 1,
                'msg' => '',
                'data' => [
                    'education' => '学历要求',
                    'major' => '专业要求',
                    'recruitAmount' => '招聘人数',
                    'applyType' => '报名方式'
                ]
            ];
            
        case '/admin/specialNeed/configCreate':
            return [
                'result' => 1,
                'msg' => '创建成功',
                'data' => ['id' => 123]
            ];
            
        default:
            return [
                'result' => 1,
                'msg' => '操作成功',
                'data' => []
            ];
    }
}

// 示例1: 获取字段选项
echo "1. 获取职位配置的字段选项:\n";
$response = mockApiCall('GET', '/admin/specialNeed/getFieldOptions?type=job');
echo "响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n\n";

echo "前端显示的字段选项:\n";
foreach ($response['data'] as $key => $label) {
    echo "  - {$label} (字段名: {$key})\n";
}

echo "\n注意: 不再有重复的字段选项！\n";
echo "  ✅ 只有一个'专业要求'选项 (major)\n";
echo "  ✅ 只有一个'招聘人数'选项 (recruitAmount)\n";
echo "  ✅ 只有一个'报名方式'选项 (applyType)\n\n";

// 示例2: 创建配置
echo "2. 创建专业要求配置:\n";
$configData = [
    'SpecialNeedConfig' => [
        'name' => '电子科技大学专业修改',
        'type' => 'job',
        'target_id' => 301480,
        'field_name' => 'major',  // 使用统一的主字段名
        'field_value' => '计算机科学与技术、软件工程、网络工程',
        'platform' => 'ALL',
        'status' => 1,
        'remark' => '统一专业要求配置'
    ]
];

$response = mockApiCall('POST', '/admin/specialNeed/configCreate', $configData);
echo "响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n\n";

// 示例3: 创建招聘人数配置
echo "3. 创建招聘人数配置:\n";
$configData = [
    'SpecialNeedConfig' => [
        'name' => '某职位招聘人数修改',
        'type' => 'job',
        'target_id' => 301480,
        'field_name' => 'recruitAmount',  // 使用统一的字段名
        'field_value' => '若干',
        'platform' => 'ALL',
        'status' => 1,
        'remark' => '设置招聘人数为若干'
    ]
];

$response = mockApiCall('POST', '/admin/specialNeed/configCreate', $configData);
echo "响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n\n";

// 示例4: 创建报名方式配置
echo "4. 创建报名方式配置:\n";
$configData = [
    'SpecialNeedConfig' => [
        'name' => '某公告报名方式修改',
        'type' => 'announcement',
        'target_id' => 251855,
        'field_name' => 'applyType',  // 使用统一的字段名
        'field_value' => '电子邮件,现场报名',
        'platform' => 'ALL',
        'status' => 1,
        'remark' => '设置报名方式'
    ]
];

$response = mockApiCall('POST', '/admin/specialNeed/configCreate', $configData);
echo "响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n\n";

// 示例5: 数据应用效果演示
echo "5. 数据应用效果演示:\n";

// 模拟职位数据（使用amount字段）
$jobData = [
    'id' => 301480,
    'title' => '高级软件工程师',
    'amount' => 5,              // 原始数据使用amount
    'major' => '120,121',       // 原始数据使用major ID
    'applyType' => '1,2'        // 原始数据使用applyType ID
];

echo "原始职位数据:\n";
echo json_encode($jobData, JSON_UNESCAPED_UNICODE) . "\n\n";

// 模拟配置应用后的数据
$modifiedJobData = [
    'id' => 301480,
    'title' => '高级软件工程师',
    'amount' => '若干',         // recruitAmount配置应用到amount字段
    'major' => '计算机科学与技术、软件工程、网络工程', // major配置应用
    'applyType' => '电子邮件,现场报名' // applyType配置应用
];

echo "应用配置后的职位数据:\n";
echo json_encode($modifiedJobData, JSON_UNESCAPED_UNICODE) . "\n\n";

echo "智能转换说明:\n";
echo "  - recruitAmount配置 → 应用到amount字段 (职位数据结构)\n";
echo "  - major配置 → 应用到major字段 (覆盖原有ID)\n";
echo "  - applyType配置 → 应用到applyType字段 (覆盖原有ID)\n\n";

// 示例6: 公告数据应用
echo "6. 公告数据应用示例:\n";

// 模拟公告数据（使用recruitAmount、majorTxt字段）
$announcementData = [
    'id' => 251855,
    'title' => '2024年春季招聘公告',
    'recruitAmount' => 20,           // 公告使用recruitAmount
    'majorTxt' => '软件工程相关专业',  // 公告使用majorTxt文本
    'applyTypeText' => '网上报名'     // 公告使用applyTypeText文本
];

echo "原始公告数据:\n";
echo json_encode($announcementData, JSON_UNESCAPED_UNICODE) . "\n\n";

// 模拟配置应用后的数据
$modifiedAnnouncementData = [
    'id' => 251855,
    'title' => '2024年春季招聘公告',
    'recruitAmount' => '若干',                    // recruitAmount配置直接应用
    'majorTxt' => '计算机科学与技术、软件工程、网络工程', // major配置应用到majorTxt字段
    'applyTypeText' => '电子邮件,现场报名'         // applyType配置应用到applyTypeText字段
];

echo "应用配置后的公告数据:\n";
echo json_encode($modifiedAnnouncementData, JSON_UNESCAPED_UNICODE) . "\n\n";

echo "智能转换说明:\n";
echo "  - recruitAmount配置 → 应用到recruitAmount字段 (公告数据结构)\n";
echo "  - major配置 → 应用到majorTxt字段 (公告使用文本字段)\n";
echo "  - applyType配置 → 应用到applyTypeText字段 (公告使用文本字段)\n\n";

echo "=== 总结 ===\n";
echo "✅ 前端用户只看到统一的字段选项，不再有重复\n";
echo "✅ 数据库存储使用主字段名 (major, applyType, recruitAmount)\n";
echo "✅ 系统智能根据数据结构选择实际应用的字段\n";
echo "✅ 保持向后兼容，现有数据和接口不受影响\n";
echo "✅ API接口返回清晰的字段选项，避免用户困惑\n";

echo "\n=== 示例完成 ===\n";
